// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class AppStrings {
  static const BusatySchool = 'BusatySchool';
  static const checkPassword = 'checkPassword';
  static const checkEmail = 'checkEmail';
  static const home = 'home';
  static const username = 'username';
  static const code = 'code';
  static const line = 'line';
  static const birthdate = 'birthdate';
  static const classrooms = 'classrooms';
  static const classroom = 'classroom';
  static const add_classroom = 'add_classroom';
  static const classroom_name = 'classroom_name';
  static const options = 'options';
  static const children = 'children';
  static const loginWithGoogle = 'login_with_google';
  static const checkConnection = 'check_connection';
  static const accountDisabled = 'account_disabled';
  static const loginFailed = 'login_failed';
  static const new_address = 'new_address';
  static const add_student_to_bus = 'add_student_to_bus';
  static const login = 'login';
  static const email = 'email';
  static const password = 'password';
  static const notHaveAccount = 'notHaveAccount';
  static const createAccount = 'createAccount';
  static const bus_name = 'bus_name';
  static const forgetPassword = 'forgetPassword';
  static const remember = 'remember';
  static const notes = 'notes';
  static const signup = 'signup';
  static const name = 'name';
  static const schoolName = 'schoolName';
  static const phoneNumber = 'phoneNumber';
  static const haveAccount = 'haveAccount';
  static const sendCodeAgain = 'sendCodeAgain';
  static const sendCode = 'sendCode';
  static const forget = 'forget';
  static const sendCodeRegister = 'sendCodeRegister';
  static const againPassword = 'againPassword';
  static const changePassword = 'changePassword';
  static const createPassword = 'createPassword';
  static const createNewPassword = 'createNewPassword';
  static const passwordChangeSuccess = 'passwordChangeSuccess';
  static const goHome = 'goHome';
  static const next = 'next';
  static const searchStudentHint = 'searchStudentHint';
  static const searchDriverHint = 'searchDriverHint';
  static const addByHand = 'addByHand';
  static const addByFile = 'addByFile';
  static const studentName = 'studentName';
  static const bloodType = 'bloodType';
  static const dateOfBirth = 'dateOfBirth';
  static const educationalLevel = 'educationalLevel';
  static const city = 'city';
  static const addBus = 'addBus';
  static const add = 'add';
  static const addFile = 'addFile';
  static const address = 'address';
  static const nationalId = 'nationalId';
  static const allDrivers = 'allDrivers';
  static const allSupervisors = 'allSupervisors';
  static const searchSupervisorHint = 'searchSupervisorHint';
  static const addStudent = 'addStudent';
  static const editStudent = 'editStudent';
  static const addStudentFile = 'addStudentFile';
  static const studentData = 'studentData';
  static const DriveData = 'DriveData';
  static const SupervisorData = 'SupervisorData';
  static const showStudentData = 'showStudentData';
  static const addDriver = 'addDriver';
  static const editDriver = 'editDriver';
  static const addSupervisor = 'addSupervisor';
  static const editSupervisor = 'editSupervisor';
  static const itinerary = 'itinerary';
  static const busNumber = 'busNumber';
  static const supervisor = 'supervisor';
  static const driver = 'driver';
  static const allBus = 'allBus';
  static const searchBusHint = 'searchBusHint';
  static const addStudentBusFile = 'addStudentBusFile';
  static const show = 'show';
  static const delete = 'delete';
  static const edit = 'edit';
  static const addStudentManually = 'addStudentManually';
  static const addStudentFromFile = 'addStudentFromFile';
  static const showStudent = 'showStudent';
  static const addStudentToBusFromFile = 'addStudentToBusFromFile';
  static const addStudentToBusManually = 'addStudentToBusManually';
  static const search = 'search';
  static const setting = 'setting';
  static const languages = 'languages';
  static const help = 'help';
  static const profile = 'profile';
  static const english = 'english';
  static const arabic = 'arabic';
  static const updateProfile = 'updateProfile';
  static const parent = 'parent';
  static const searchParentHint = 'searchParentHint';
  static const showParent = 'showParent';
  static const parentData = 'parentData';
  static const newPassword = 'newPassword';
  static const confirmPassword = 'confirmPassword';
  static const oldPassword = 'oldPassword';
  static const save = 'save';
  static const allRequests = 'allRequests';
  static const newRequests = 'newRequests';
  static const requestsPending = 'requestsPending';
  static const acceptRequest = 'acceptRequest';
  static const refusalRequest = 'refusalRequest';
  static const requestChangeAddress = 'requestChangeAddress';
  static const accept = 'accept';
  static const refusal = 'refusal';
  static const reasonRefusal = 'reasonRefusal';
  static const refuseRequest = 'refuseRequest';
  static const showRequest = 'showRequest';
  static const validPhone = 'validPhone';
  static const validName = 'validName';
  static const validAddress = 'validAddress';
  static const validEmail = 'validEmail';
  static const validPassword = 'validPassword';
  static const validConfirmPassword = 'validConfirmPassword';
  static const supervisors = 'supervisors';
  static const drivers = 'drivers';
  static const students = 'students';
  static const parents = 'parents';
  static const bus = 'bus';
  static const requestsChangeAddress = 'requestsChangeAddress';
  static const exit = 'exit';
  static const validNewPassword = 'validNewPassword';
  static const validConfirmedNewPassword = 'validConfirmedNewPassword';
  static const validOldPassword = 'validOldPassword';
  static const addStudentByLine = 'addStudentByLine';
  static const type = 'type';
  static const grade = 'grade';
  static const birthDate = 'birthDate';
  static const phone = 'phone';
  static const studentNotFound = 'studentNotFound';
  static const supervisorNotFound = 'supervisorNotFound';
  static const busesNotFound = 'busesNotFound';
  static const requestAbsencesNotFound = 'requestAbsencesNotFound';
  static const Absence = 'Absence';
  static const driverNotFound = 'driverNotFound';
  static const parentsNotFound = 'parentsNotFound';
  static const carNumber = 'carNumber';
  static const gender = 'gender';
  static const religion = 'religion';
  static const typeBlood = 'typeBlood';
  static const getLocations = 'getLocations';
  static const locationDone = 'locationDone';
  static const setLocation = 'setLocation';
  static const email_verified = 'email_verified';
  static const wrong_code = 'wrong_code';
  static const location = 'location';
  static const supervisor_current_location = 'supervisor_current_location';
  static const driver_current_location = 'driver_current_location';
  static const is_required = 'is_required';
  static const absence_requests = 'absence_requests';
  static const selectGrade = 'selectGrade';
  static const selectGender = 'selectGender';
  static const selectReligion = 'selectReligion';
  static const selectBloodType = 'selectBloodType';
  static const selectBus = 'selectBus';
  static const successfully_done = 'successfully_done';
  static const copy = 'copy';
  static const student = 'student';
  static const copied = 'copied';
  static const not_required = 'not_required';
  static const required = 'required';
  static const morning_trip = 'morning_trip';
  static const evening_trip = 'evening_trip';
  static const full_day = 'full_day';
  static const extract_excel = 'extract_excel';
  static const download_pdf = 'download_pdf';
  static const student_address = 'student_address';
  static const select_edu_lev = 'select_edu_lev';
  static const download_example_file = 'download_example_file';
  static const students_file = 'students_file';
  static const bus_subscription = 'bus_subscription';
  static const not_found = 'not_found';
  static const driver_added = 'driver_added';
  static const driver_edited = 'driver_edited';
  static const supervisor_added = 'supervisor_added';
  static const supervisor_edited = 'supervisor_edited';
  static const student_added = 'student_added';
  static const students_added = 'students_added';
  static const student_edited = 'student_edited';
  static const busStudents = 'busStudents';
  static const call = 'call';
  static const notification = 'notification';
  static const showSons = 'showSons';
  static const sons_number = 'sons_number';
  static const sons = 'sons';
  static const notifications = 'notifications';
  static const good_morning = 'good_morning';
  static const good_evening = 'good_evening';
  static const and = 'and';
  static const not_match = 'not_match';
  static const yes = 'yes';
  static const no = 'no';
  static const selectGradeFirst = 'selectGradeFirst';
  static const selectBusFirst = 'selectBusFirst';
  static const request_status = 'request_status';
  static const accepted = 'accepted';
  static const refused = 'refused';
  static const newS = 'newS';
  static const duplicated_user = 'duplicated_user';
  static const editBus = 'editBus';
  static const busName = 'busName';
  static const notFound = 'notFound';
  static const goodMorning = 'goodMorning';
  static const goodEvening = 'goodEvening';
  static const morningTrip = 'morningTrip';
  static const isRequired = 'isRequired';
  static const addClassroom = 'addClassroom';
  static const duplicatedUser = 'duplicatedUser';
  static const addStudentToBus = 'addStudentToBus';
  static const driverAdded = 'driverAdded';
  static const driverEdited = 'driverEdited';
  static const eveningTrip = 'eveningTrip';
  static const fullDay = 'fullDay';
  static const busSubscription = 'busSubscription';
  static const studentAddress = 'studentAddress';
  static const notRequired = 'notRequired';
  static const driverCurrentLocation = 'driverCurrentLocation';
  static const driveData = 'driveData';
  static const newAddress = 'newAddress';
  static const supervisorCurrentLocation = 'supervisorCurrentLocation';
  static const wrongCode = 'wrongCode';
  static const absenceRequests = 'absenceRequests';
  static const requestStatus = 'requestStatus';
  static const newRequest = 'newRequest';
  static const successfullyDone = 'successfullyDone';
  static const sonsNumber = 'sonsNumber';
  static const notMatch = 'notMatch';
  static const extractExcel = 'extractExcel';
  static const downloadPDF = 'downloadPDF';
  static const supervisorEdited = 'supervisorEdited';
  static const supervisorAdded = 'supervisorAdded';
  static const studentsAdded = 'studentsAdded';
  static const selectEduLev = 'selectEduLev';
  static const classroomName = 'classroomName';
  static const studentsFile = 'studentsFile';
  static const studentEdited = 'studentEdited';
  static const studentAdded = 'studentAdded';
  static const downloadExampleFile = 'downloadExampleFile';
  static const checkInternetConnection = 'checkInternetConnection';
  static const openTrips = 'openTrips';
  static const tripTypes = 'tripTypes';
  static const supervisorName = 'supervisorName';
  static const old_address = 'old_address';
  static const track = 'track';
  static const WrongEmail = 'WrongEmail';
  static const driverName = 'driverName';
  static const deleteAccountTitle = 'deleteAccountTitle';
  static const deleteAccountConfirm = 'deleteAccountConfirm';
  static const deleteAccountNote = 'deleteAccountNote';
  static const cancel = 'cancel';
  static const deleteAccount = 'deleteAccount';
  static const wrongEmail = 'wrongEmail';
  static const contactUs = 'contact_us';
  static const getInTouch = 'get_in_touch';
  static const wedLoveToHear = 'wed_love_to_hear';
  static const enterYourName = 'enter_your_name';
  static const enterYourEmail = 'enter_your_email';
  static const describeProblem = 'describe_problem';
  static const pleaseEnterName = 'please_enter_name';
  static const pleaseEnterEmail = 'please_enter_email';
  static const pleaseValidEmail = 'please_valid_email';
  static const pleaseDescribeProblem = 'please_describe_problem';
  static const messageTooLong = 'message_too_long';
  static const contactDirectly = 'contact_directly';
  static const emailCopied = 'email_copied';
  static const sending = 'sending';
  static const emailSent = 'email_sent';
  static const failedToSend = 'failed_to_send';
  static const privacyPolicy = 'privacy_policy';
  static const privacyPolicyTitle = 'privacy_policy_title';
  static const privacyPolicyContent = 'privacy_policy_content';
  static const playStore = 'play_store';
  static const rateApp = 'rate_app';
  static const shareApp = 'share_app';
  static const moreApps = 'more_apps';
  static const SHARE = 'SHARE';
  static const pro = 'pro';
  static const selectAbsenceDate = 'select_absence_date';
  static const currentPassword = 'current_password';
  static const addSon = 'addSon';
  static const sonData = 'sonData';
  static const sonLocation = 'sonLocation';
  static const showSonOnMap = 'showSonOnMap';
  static const requestAbsence = 'requestAbsence';
  static const date = 'date';
  static const yourAddresses = 'yourAddresses';
  static const addManualAddress = 'addManualAddress';
  static const addAddressFromMap = 'addAddressFromMap';
  static const addNewAddress = 'addNewAddress';
  static const country = 'country';
  static const nameSon = 'nameSon';
  static const nameParent = 'nameParent';
  static const cityOrRegin = 'cityOrRegin';
  static const streetName = 'streetName';
  static const phase = 'phase';
  static const addAddress = 'addAddress';

  static const stage = 'stage';
  static const absenteeismRequests = 'absenteeismRequests';
  static const searchingAddressOnMap = 'searchingAddressOnMap';
  static const logout = 'logout';

  static const emailVerified = 'email_verified';

  static const settings = 'settings';

  static const gradeEdu = 'grade_edu';
  static const school = 'school';
  static const addNewRequest = 'add_new_request';
  static const chooseStudent = 'choose_student';
  static const chooseTripType = 'choose_trip_type';
  static const locationOnMap = 'location_on_map';
  static const sendSuccessfully = 'send_successfully';
  static const sendFailed = 'send_failed';
  static const send = 'send';

  static const coupon = 'coupon';
  static const enterCoupon = 'enterCoupon';
  static const subscribe = 'subscribe';
  static const benefits = 'benefits';
  static const subscribeWithCoupon = 'subscribe_with_coupon';
  static const alreadySubscribed = 'already_subscribed';
  static const or = 'or';

  static const subscribedSuccessfully = 'subscribed_successfully';

  static const noTrips = 'no_trips';

  static const tripLoading = 'trip_loading';
  static const alreadyRequest = 'already_request';

  static const all = 'all';
  static const unRead = 'unRead';
  static const benefitBusatyBro = 'benefitBusatyBro';
  static const withoutAds = 'withoutAds';
  static const trackingSonInMoment = 'trackingSonInMoment';
  static const tackingBuysInMoment = 'tackingBuysInMoment';

  static const String subscribeToTrack = 'subscribe_to_track';
  static const String subscribeNow = 'subscribe_now';
  static const String trackingDescription = 'trackingDescription';
  static const String termsAndConditions = 'terms_and_conditions';
  static const subscriptionRequired = 'subscriptionRequired';
  static const subscriptionMessage = 'subscriptionMessage';

  static const String applyCoupon = 'apply_coupon';
  static const String apply = 'apply';
  static const String subscription = 'subscription';
  static const enterCouponDescription = 'enter_coupon_description';

  static const realTimeNotifications = 'realTimeNotifications';
  static const realTimeNotificationsDesc = 'realTimeNotificationsDesc';
  static const liveTracking = 'liveTracking';
  static const liveTrackingDesc = 'liveTrackingDesc';
  static const tripHistory = 'tripHistory';
  static const tripHistoryDesc = 'tripHistoryDesc';
  static const prioritySupport = 'prioritySupport';
  static const prioritySupportDesc = 'prioritySupportDesc';

  static const busManagement = 'busManagement';
  static const busManagementDesc = 'busManagementDesc';
  static const staffManagement = 'staffManagement';
  static const staffManagementDesc = 'staffManagementDesc';
  static const realTimeTracking = 'realTimeTracking';
  static const realTimeTrackingDesc = 'realTimeTrackingDesc';
  static const attendanceSystem = 'attendanceSystem';
  static const attendanceSystemDesc = 'attendanceSystemDesc';

  static const String updateRequired = "update_required";
  static const String updateMessage = "update_message";
  static const String update = "update";
  static const String busatySchoolTitle = "Busaty - School";

  // Location map widget
  static const String studentLocation = "student_location";
  static const String locationNotAvailable = "location_not_available";
  static const String latitude = "latitude";
  static const String longitude = "longitude";
  static const String viewOnMap = "view_on_map";
  static const String openInMaps = "open_in_maps";
  static const String showFullMap = "show_full_map";

  // Adding new translation keys
  static const startDate = "startDate";
  static const endDate = "endDate";
  static const remainingTime = "remainingTime";
  static const expired = "expired";
  static const months = "months";
  static const days = "days";
  static const unknown = "unknown";

  // Complete Profile Screen translations
  static const completeProfile = "complete_profile";
  static const personalInformation = "personal_information";
  static const uploadPhoto = "upload_photo";
  static const pleaseEnterValidName = "please_enter_valid_name";
  static const pleaseEnterValidPhone = "please_enter_valid_phone";
  static const pleaseEnterValidAddress = "please_enter_valid_address";
  static const pleaseUploadPhoto = "please_upload_photo";
  static const profileCompleted = "profile_completed";
  static const somethingWentWrong = "something_went_wrong";
  static const submit = "submit";

  // Previous Trips Screen
  static const previousTrips = "previousTrips";
  static const noTripsYet = "noTripsYet";
  static const filterByBus = "filterByBus";
  static const filterByDate = "filterByDate";
  static const tripStartTime = "tripStartTime";
  static const tripEndTime = "tripEndTime";
  static const viewRoute = "viewRoute";
  static const viewAttendance = "viewAttendance";
  static const applyFilters = "applyFilters";
  static const resetFilters = "resetFilters";
  static const tripType = "tripType";

  // Attendance Screen
  static const attendance = "attendance";
  static const present = "present";
  static const absent = "absent";
  static const attendanceDetails = "attendanceDetails";
  static const studentsList = "studentsList";
  static const noStudentsPresent = "noStudentsPresent";
  static const noStudentsAbsent = "noStudentsAbsent";
  static const attendanceDate = "attendanceDate";
  static const attendanceStatus = "attendanceStatus";

  // Route Map Screen
  static const routeMap = "routeMap";
  static const tripRoute = "tripRoute";
  static const startPoint = "startPoint";
  static const endPoint = "endPoint";
  static const busRoute = "busRoute";
  static const routeDetails = "routeDetails";
  static const totalDistance = "totalDistance";
  static const estimatedTime = "estimatedTime";
  static const noRouteData = "noRouteData";
  static const kilometers = "kilometers";
  static const minutes = "minutes";

  // Trip Details Screen
  static const tripDetails = "tripDetails";
  static const viewDetails = "viewDetails";
  static const tripDate = "tripDate";
  // Notification Settings
  static const notificationSettings = "notification_settings";
  static const initializeNotifications = "initialize_notifications";
  static const tripNotifications = "trip_notifications";
  static const addressChangeNotifications = "address_change_notifications";
  static const absenceNotifications = "absence_notifications";
  static const enableTripNotifications = "enable_trip_notifications";
  static const enableAddressChangeNotifications =
      "enable_address_change_notifications";
  static const enableAbsenceNotifications = "enable_absence_notifications";

  // Request Type Tab Screen
  static const permanent = "permanent";
  static const temporary = "temporary";
  static const comingSoon = "coming_soon";
}
