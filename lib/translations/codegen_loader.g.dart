// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader {
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String, dynamic> ar = {
    
    "BusatySchool": "باصاتي - المدرسة",
    "checkPassword": "كلمة المرور ليست متطابقة",
    "checkEmail": "من فضلك راجع البريد الإلكتروني للتحقق من الكود",
    "home": "الرئيسية",
    "username": "اسم المستخدم",
    "code": "كود",
    "line": "خط",
     "pro": "باصاتي PRO",
    "birthdate": "تاريخ الميلاد",
    "classrooms": "الصفوف",
    "classroom": "الصف",
    "add_classroom": "إضافة صف جديد",
    "classroom_name": "اسم الصف",
    "options": "خيارات",
    "children": "الأولاد",
    "new_address": "العنوان الجديد",
    "add_student_to_bus": "أضف طلاب إلى الباص",
    "login": "تسجيل الدخول",
    "email": "البريد الالكتروني",
    "password": "كلمة المرور",
    "notHaveAccount": "ليس لديك حساب؟ ",
    "createAccount": "إنشاء حساب",
    "bus_name": "اسم الباص",
    "forgetPassword": "نسيت كلمة المرور؟ ",
    "remember": "تذكر",
    "notes": "ملاحظات",
    "signup": "تسجيل",
    "name": "الاسم",
    "schoolName": "اسم المدرسة",
    "phoneNumber": "رقم الهاتف",
    "haveAccount": "لديك حساب؟ ",
    "sendCodeAgain": "إرسال الكود مرة أخرى",
    "sendCode": "إرسال الكود",
    "forget": "نسيت كلمة المرور",
    "sendCodeRegister":
        "سيتم ارسال كود الي البريد الالكتروني المسجل به لإستعادة الحساب",
    "againPassword": "إعادة كلمة المرور",
    "changePassword": "تغيير كلمة المرور",
    "passwordChangeSuccess": "لقد تم تغير كلمة المرور بنجاح",
    "goHome": "الذهاب إلى الرئيسية",
    "next": "متابعة",
    "searchStudentHint": "البحث عن طالب",
    "searchDriverHint": "البحث عن سائق",
    "addByHand": "إضافة طالب يدوي ",
    "addByFile": "إضافة طلاب من ملف",
    "studentName": "اسم الطالب",
    "bloodType": "فصيلة الدم",
    "dateOfBirth": "تاريخ الميلاد",
    "educationalLevel": "المرحلة الدراسية",
    "city": "المدينة",
    "addBus": "أضف باص",
    "add": "إضافة",
    "addFile": "إضافة ملف",
    "address": "العنوان",
    "nationalId": "الرقم القومي",
    "allDrivers": "جميع السائقين",
    "allSupervisors": "جميع المشرفين",
    "searchSupervisorHint": "البحث عن مشرف",
    "addStudent": "إضافة طالب",
    "editStudent": "تعديل بيانات الطالب",
    "addStudentFile": "إضافة مجموعة طلاب",
    "studentData": "بيانات الطالب",
    "DriveData": "بيانات السائق",
    "SupervisorData": "بيانات المشرف",
    "showStudentData": "عرض بيانات الطالب",
    "addDriver": "إضافة سائق",
    "editDriver": "تعديل بيانات السائق",
    "addSupervisor": "إضافة مشرف",
    "editSupervisor": "تعديل بيانات المشرف",
    "itinerary": "خط سير",
    "busNumber": "رقم الباص",
    "supervisor": "المشرف",
    "driver": "السائق",
    "allBus": "جميع الباصات",
    "searchBusHint": "البحث عن باص",
    "addStudentBusFile": "إضافة طلاب للباص من ملف",
    "show": "عرض",
    "delete": "حذف",
    "edit": "تعديل",
    "addStudentManually": "إضافة طالب يدويا",
    "addStudentFromFile": "إضافة طلاب من ملف",
    "showStudent": "عرض الطلاب",
    "addStudentToBusFromFile": "إضافة طلاب للباص من ملف",
    "addStudentToBusManually": "إضافة طلاب للباص يدوي",
    "search": "البحث",
    "setting": "الإعدادات",
    "languages": "اللغة",
    "help": "مساعدة",
    "profile": "الملف الشخصي",
    "english": "English",
    "arabic": "العربية",
    "updateProfile": "تعديل الملف الشخصي",
    "parent": "ولي الأمر",
    "searchParentHint": "البحث عن ولي أمر",
    "showParent": "عرض بيانات ولي الأمر",
    "parentData": "بيانات ولي الأمر",
    "newPassword": "كلمة المرور الجديدة",
    "confirmPassword": "تأكيد كلمة المرور",
    "oldPassword": "كلمة المرور القديمة",
    "save": "حفظ",
    "allRequests": "جميع الطلبات",
    "newRequests": "الطلبات الجديدة",
    "requestsPending": "الطلبات قيد الانتظار",
    "acceptRequest": "الطلبات المقبولة",
    "refusalRequest": "الطلبات المرفوضة",
    "requestChangeAddress": "طلب تغير العنوان",
    "accept": "قبول",
    "refusal": "رفض",
    "reasonRefusal": "سبب الرفض",
    "refuseRequest": "رفض الطلب",
    "showRequest": "عرض الطلب",
    "validPhone": "رجاء أدخل رقم الهاتف ",
    "validName": "رجاء أدخل الاسم ",
    "validAddress": "رجاء أدخل العنوان",
    "validEmail": "رجاء أدخل الإيميل",
    "validPassword": "رجاء أدخل كلمة المرور",
    "validConfirmPassword": "رجاء أدخل كلمة المرور",
    "supervisors": "المشرفين",
    "drivers": "السائقين",
    "students": "الطلاب",
    "parents": "أولياء الأمور",
    "bus": "الباصات",
    "requestsChangeAddress": "طلبات تغيير العنوان",
    "exit": "الخروج",
    "validNewPassword": "رجاء أدخل كلمة المرور الجديدة",
    "validConfirmedNewPassword": "رجاء أعد إدخال كلمة المرور",
    "validOldPassword": "رجاء أدخل كلمة المرور القديمة",
    "addStudentByLine": "إضافة طالب بالخط",
    "type": "النوع",
    "grade": "المرحلة الدراسية",
    "birthDate": "تاريخ الميلاد",
    "phone": "رقم الهاتف",
    "studentNotFound": "لا يوجد طلاب",
    "supervisorNotFound": "لا يوجد مشرفين",
    "busesNotFound": "لا يوجد باصات",
    "requestAbsencesNotFound": "لا يوجد طلبات",
    "Absence": "غياب",
    "driverNotFound": "لا يوجد سائقين",
    "parentsNotFound": "لا يوجد أولياء أمور",
    "carNumber": "رقم السيارة",
    "gender": "النوع",
    "religion": "الديانة",
    "typeBlood": "فصيلة الدم",
    "getLocations": "الحصول على الموقع",
    "locationDone": "تم إضافة الموقع بنجاح",
    "setLocation": "حدد عنوانك",
    "email_verified": "تم التحقق بنجاح",
    "wrong_code": "الكود غير صحيح! من فضلك راجع الإيميل وأعد المحاولة مرة أخرى",
    "location": "الموقع",
    "supervisor_current_location": "الموقع الحالي للمشرف",
    "driver_current_location": "الموقع الحالي للسائق",
    "is_required": " مطلوب",
    "absence_requests": "طلبات الغياب",
    "selectGrade": "اختر المرحلة",
    "selectGender": "اختر النوع",
    "selectReligion": "اختر الديانة",
    "selectBloodType": "اختر فصيلة الدم",
    "selectBus": "اختر الباص",
    "successfully_done": "تم بنجاح",
    "copy": "نسخ كود ولي الأمر",
    "student": "الطالب",
    "copied": "تم نسخ الكود",
    "not_required": "غير مطلوب",
    "required": "مطلوب",
    "morning_trip": "رحلة الصباح",
    "evening_trip": "رحلة المساء",
    "full_day": "اليوم بالكامل",
    "extract_excel": "استخراج ملف Excel",
    "download_pdf": "تحميل ملف PDF",
    "student_address": "عنوان الطالب",
    "select_edu_lev": "اختر المرحلة الدراسية أولا",
    "download_example_file": "تحميل ملف Excel كمثال",
    "students_file": "ملف الطلاب (Excel)",
    "bus_subscription": "اشتراك الباص",
    "not_found": "لا يوجد",
    "driver_added": "تم إضافة السائق بنجاح",
    "driver_edited": "تم تعديل بيانات السائق بنجاح",
    "supervisor_added": "تم إضافة المشرف بنجاح",
    "supervisor_edited": "تم تعديل بيانات المشرف بنجاح",
    "student_added": "تم إضافة الطالب بنجاح",
    "students_added": "تم إضافة الطلاب بنجاح",
    "student_edited": "تم تعديل بيانات الطالب بنجاح",
    "busStudents": "طلاب الباص",
    "call": "اتصال",
    "notification": "إشعار",
    "showSons": "عرض الأبناء",
    "sons_number": "عدد الأبناء",
    "sons": "الأبناء",
    "notifications": "الإشعارات",
    "good_morning": "صباح الخير",
    "good_evening": "مساء الخير",
    "and": "و",
    "not_match": "غير متطابقتان",
    "yes": "نعم",
    "no": "لا",
    "selectGradeFirst": "اختر المرحلة الدراسية أولا",
    "selectBusFirst": "اختر الباص أولا",
    "request_status": "حالة الطلب",
    "accepted": "مقبول",
    "refused": "مرفوض",
    "newS": "جديد",
    "duplicated_user": "اسم المستخدم محجوز بالفعل، الرجاء تغييره",
    "editBus": "تعديل الباص",
    "busName": "اسم الباص",
    "notFound": "لا يوجد",
    "goodMorning": "صباح الخير",
    "goodEvening": "مساء الخير",
    "morningTrip": "رحلة صباحية",
    "isRequired": "مطلوب",
    "addClassroom": "اضافة فصل",
    "duplicatedUser": "مستخدم مكرر",
    "addStudentToBus": "اضافة طالب الي الباص",
    "driverAdded": "تم إضافة السائق",
    "driverEdited": "تم تعديل السائق",
    "eveningTrip": "رحلة مسائية",
    "fullDay": "يوم كامل",
    "busSubscription": "اشتراك الباص",
    "studentAddress": "عنوان الطالب",
    "notRequired": "غير مطلوب",
    "driverCurrentLocation": "الموقع الحالي للسائق",
    "driveData": "بيانات السائق",
    "newAddress": "عنوان جديد",
    "supervisorCurrentLocation": "الموقع الحالي للمشرف",
    "wrongCode": "رمز خاطئ",
    "absenceRequests": "طلبات الغياب",
    "requestStatus": "حالة الطلب",
    "newRequest": "طلب جديد",
    "successfullyDone": "فعلت بنجاح",
    "sonsNumber": "عدد الابناء",
    "notMatch": "غير متطابق",
    "extractExcel": "استخراج اكسل",
    "downloadPDF": "تحميل ملف",
    "supervisorEdited": "تم تعديل المشرف",
    "supervisorAdded": "تم اضافة المشرف",
    "studentsAdded": "تم اضافة طالب",
    "selectEduLev": "اختر المرحلة",
    "classroomName": "اسم الفصل الدراسي",
    "studentsFile": "ملف الطلاب",
    "studentEdited": "تم تعديل الطالب",
    "studentAdded": "تم اضافة الطالب",
    "downloadExampleFile": "تحميل ملف المثال",
    "checkInternetConnection": "يرجي التاكد من الاتصال بالانترنت",
    "openTrips": "الرحلات المفتوحة",
    "tripTypes": "نوع الرحلة",
    "supervisorName": "اسم المشرف",
    "old_address": "عنوان قديم",
    "track": "تتبع",
    "isEmailWrong": "هل البريد الإلكتروني غير صحيح؟",
    "contact_us": "اتصل بنا",
    "get_in_touch": "ابقى على تواصل",
    "wed_love_to_hear": "يسعدنا أن نسمع منك",
    "enter_your_name": "أدخل اسمك",
    "enter_your_email": "أدخل بريدك الإلكتروني",
    "describe_problem": "صف مشكلتك أو ملاحظاتك",
    "please_enter_name": "الرجاء إدخال اسمك",
    "please_enter_email": "الرجاء إدخال بريدك الإلكتروني",
    "please_valid_email": "الرجاء إدخال بريد إلكتروني صحيح",
    "please_describe_problem": "الرجاء وصف مشكلتك",
    "message_too_long": "لا يمكن أن تكون الرسالة أطول من 1000 حرف",
    "contact_directly": "أو اتصل بنا مباشرة:",
    "email_copied": "تم نسخ البريد الإلكتروني",
    "sending": "جاري الإرسال...",
    "email_sent": "تم إرسال البريد الإلكتروني بنجاح!",
    "failed_to_send": "فشل في إرسال البريد الإلكتروني: {error}",
    "wrongEmail": "هل البريد الإلكتروني غير صحيح؟",


    "Busaty - Parents": "باصاتي - ولي الأمر",
    "Religion": "الديانة",
    "select_absence_date": "اختر موعد الغياب",
    "current_password": "الباسورد الحالي",
    "NewAddress": "عنوان جديد",
    
    "sonData": "بيانات الابن",
    "showSonOnMap": "عرض الابن على الخريطة",
    "requestAbsence": "طلب غياب",
    "date": "تاريخ",
    "yourAddresses": "عناوينك",
    "addManualAddress": "أضف عنوان يدوي",
    "addAddressFromMap": "أضف عنوانك على الخريطة",
    "addNewAddress": "أضف عنوان جديد",
    "country": "دولة",
    "nameSon": "اسم الابن",
    "nameParent": "اسم ولي الامر",
    "cityOrRegin": "المدينة/المنطقة",
    "streetName": "اسم الشارع",
    "phase": "مرحلة",
    "addAddress": "إضافة عنوان",
    "stage": "المرحلة",
    "absenteeismRequests": "الغياب",
    "searchingAddressOnMap": "بحث العنوان على الخريطة",
    "logout": "تسجيل الخروج",
    "sonLocation": "موقع الابن الحالي",
    "settings": "الإعدادات",
    
    "birth_date": "تاريخ الميلاد",
    "grade_edu": "المرحلة الدراسية",
    "school": "المدرسة",
    "add_new_request": "أضف طلب جديد",
    "choose_student": "اختر الطالب",
    "choose_trip_type": "اختر نوع الرحلة",
    "location_on_map": "الموقع على الخريطة",
    "send_successfully": "تم إرسال طلبك بنجاح",
    "send_failed": "فشل، تأكد من إدخال جميع البيانات بشكل صحيح",
    "send": "إرسال",
   
    "coupon": "الكوبون",
    "enter_coupon": "أدخل كود الكوبون",
    "subscribe": "اشترك",
    "benefits": "المميزات",
    "subscribe_with_coupon": "اشترك باستخدام الكوبون",
    "or": "أو",
    "subscribed_successfully": "تم الاشتراك بنجاح",
  
    "no_trips": "لا يوجد رحلات متاحة في الوقت الحالي!",
    "already_subscribed": "أنت مشترك بالفعل",
  
    "trip_loading": "جاري تحميل الرحلة، برجاء الانتظار...",
    "already_request": "لديك طلب تغيير عنوان بالفعل لهذا الطالب",
    
    "all": "الكل",
    "unRead": "غير مقروءه",
    "benefitBusatyBro": "مميزات الاشترك في باصاتي برو",
    "withoutAds": "بدون اعلانات",
    "trackingSonInMoment": "تابع ابنك لحظيا خلال وجوده في الباص",
    "tackingBuysInMoment": "تابع تحركات الباص وهو في طريقه اليك",
    "deleteAccountTitle": "تأكيد حذف الحساب",
    "deleteAccountConfirm": "هل أنت متأكد من حذف حسابك بشكل نهائي؟",
    "deleteAccountWarning": "هل أنت متأكد من حذف حسابك نهائِ؟",
    "deleteAccountNote":
        "سيتم حذف جميع بياناتك من نظامنا تلقائِ بعد 30 يومِ",
    "deleteAccount": "حذف الحساب",
    "cancel": "إلغاء",
    "terms_and_conditions": "الشروط والاحكام",
    "subscriptionRequired": "الاشتراك مطلوب",
    "subscriptionMessage": "يرجى الاشتراك للوصول إلى هذه الميزة",
    "enter_coupon_description": "أدخل كود الكوبون للاشتراك في الخدمة المميزة"
  };
  static const Map<String, dynamic> en = {
    "BusatySchool": "Busaty - School",
    "checkPassword": "Check your Password",
    "checkEmail":
        "Email Created Successfully now check your email for verification code",
    "home": "Home",
    "username": "Username",
      "pro": "Busaty PRO",

    "code": "Code",
    "Absence": "Absence",
    "line": "Line",
    "classroom": "Classroom",
    "classrooms": "Classroom",
    "add_classroom": "Add new Classroom",
    "classroom_name": "Classroom Name",
    "birthdate": "Date of Birth",
    "options": "Options",
    "children": "Children",
    "new_address": "New Address",
    "add_student_to_bus": "Add Students to Bus",
    "bus_name": "Bus name",
    "login": "Login",
    "notes": "Notes",
    "email": "Email",
    "password": "Password",
    "notHaveAccount": "Not Have Account ? ",
    "createAccount": "Create Account",
    "forgetPassword": "Forget Password ? ",
    "remember": "Remember",
    "signup": "Signup",
    "name": "Name",
    "schoolName": "School Name",
    "phoneNumber": "Phone Number",
    "confirmPassword": "Confirm Password",
    "haveAccount": "Have Account ? ",
    "sendCodeAgain": "send code again",
    "sendCode": "Send Code",
    "forget": "Forget Password",
    "sendCodeRegister":
        "code will be sent to the registered email to recover the account",
    "newPassword": "New Password",
    "againPassword": "Write Again Password",
    "passwordChangeSuccess": "Password Change Success",
    "goHome": "Go to Home",
    "next": "Next",
    "searchStudentHint": "search the student",
    "searchDriverHint": "search the driver",
    "addByHand": "Add student ",
    "addByFile": "Add students file",
    "studentName": "student name",
    "bloodType": "blood type",
    "dateOfBirth": "Date of birth",
    "educationalLevel": "educational level",
    "city": "city",
    "addBus": "add bus",
    "add": "Add",
    "addFile": "Add File",
    "address": "Address",
    "nationalId": "National ID",
    "allDrivers": "All Drivers",
    "allSupervisors": "All Supervisors",
    "searchSupervisorHint": "Search Supervisor",
    "addStudent": "Add Student",
    "editStudent": "Edit Student Data",
    "addStudentFile": "Add Student File",
    "studentData": "Student Data",
    "students": "Students",
    "DriveData": "Driver Data",
    "SupervisorData": "Supervisor Data",
    "showStudentData": "Student Data",
    "addDriver": "Add Driver",
    "editDriver": "Edit Driver Data",
    "addSupervisor": "Add Supervisor",
    "editSupervisor": "Edit Supervisor Data",
    "itinerary": "ITinerary",
    "busNumber": "Bus Number",
    "supervisor": "Supervisor",
    "driver": "Driver",
    "allBus": "All Bus",
    "searchBusHint": "Search Bus",
    "addStudentBusFile": "Add Student to Bus From File",
    "show": "Show",
    "edit": "Edit",
    "delete": "Delete",
    "addStudentManually": "Add Student Manually",
    "addStudentFromFile": "Add Student From File",
    "showStudent": "Show Student",
    "addStudentToBusFromFile": "Add Student To Bus From File",
    "addStudentToBusManually": "Add Student To Bus Manually",
    "search": "Search",
    "setting": "Setting",
    "languages": "Languages",
    "help": "Help",
    "profile": "Profile",
    "english": "English",
    "arabic": "العربية",
    "updateProfile": "Update Profile",
    "changePassword": "Change Password",
    "parent": "Parent",
    "searchParentHint": "Search Parent",
    "showParent": "Show Parent",
    "parentData": "Parent Data",
    "oldPassword": "Old Password",
    "save": "Save",
    "allRequests": "All Requests",
    "newRequests": "New Requests",
    "requestsPending": "Pending Request",
    "acceptRequest": "Accept Requests",
    "refusalRequest": "Refusal Request",
    "requestChangeAddress": "Request Change Address",
    "accept": "Accept",
    "refusal": "Refusal",
    "reasonRefusal": "Reason Refusal",
    "refuseRequest": "Refuse Requests",
    "showRequest": "Show Request",
    "validPhone": "please write phone number",
    "validName": "please write name",
    "validAddress": "please write address",
    "validEmail": "please write email",
    "validPassword": "please write password",
    "validConfirmPassword": "please write confirmed password",
    "supervisors": "Supervisors",
    "drivers": "Drivers",
    "parents": "Parents",
    "bus": "Buses",
    "requestsChangeAddress": "Change Address Requests",
    "exit": "Exit",
    "validNewPassword": "please enter new password",
    "validConfirmedNewPassword": "please enter confirmed new password",
    "validOldPassword": "please enter old password",
    "addStudentByLine": "Add Student By Line",
    "type": "Type",
    "grade": "Grade",
    "birthDate": "Birth Date",
    "phone": "Phone",
    "studentNotFound": "Students Not Found",
    "busesNotFound": "Buses Not Found",
    "requestAbsencesNotFound": "There are no requests",
    "driverNotFound": "Drivers Not Found",
    "parentsNotFound": "Parents Not Found",
    "carNumber": "Car Number",
    "gender": "Gender",
    "religion": "Religion",
    "typeBlood": "Type Blood",
    "getLocations": "Get Locations",
    "locationDone": "Add Location Successful",
    "setLocation": "Set Your Location",
    "email_verified": "Your email is verified.",
    "wrong_code": "wrong code! Please check your email and try again",
    "location": "Location",
    "supervisor_current_location": "Supervisor Current Location",
    "driver_current_location": "Driver Current Location",
    "is_required": " is required",
    "absence_requests": "Absence Requests",
    "selectGrade": "Select Grade",
    "selectGender": "Select Gender",
    "selectReligion": "Select Religion",
    "selectBloodType": "Select Blood Type",
    "selectBus": "Select Bus",
    "successfully_done": "Successfully Done",
    "copy": "Copy Parent's Code",
    "student": "Student",
    "copied": "Code Copied",
    "not_required": "Not Required",
    "required": "Required",
    "morning_trip": "Morning Trip",
    "evening_trip": "Evening Trip",
    "full_day": "Full Day",
    "extract_excel": "Extract Excel File",
    "download_pdf": "Download PDF File",
    "student_address": "Student Address",
    "select_edu_lev": "Select Education level first",
    "download_example_file": "Download Example Excel file",
    "students_file": "Students File (Excel)",
    "bus_subscription": "Bus Subscription",
    "not_found": "Not Found",
    "driver_added": "Driver added successfully",
    "driver_edited": "Driver edited successfully",
    "supervisor_added": "Supervisor added successfully",
    "supervisor_edited": "Supervisor edited successfully",
    "student_added": "Student added successfully",
    "students_added": "Students added successfully",
    "student_edited": "Student edited successfully",
    "busStudents": "Bus Students",
    "call": "Call",
    "showSons": "Show Sons",
    "sons_number": "Sons Number",
    "sons": "Sons",
    "notifications": "Notifications",
    "good_morning": "Good Morning",
    "good_evening": "Good Evening",
    "and": "and",
    "not_match": "doesn't match",
    "yes": "Yes",
    "no": "No",
    "selectGradeFirst": "Select Grade First",
    "selectBusFirst": "Select Bus First",
    "request_status": "Request Status",
    "accepted": "Accepted",
    "refused": "Refused",
    "newS": "New",
    "duplicated_user": "Username is already reserved, please change it",
    "editBus": "Edit Bus",
    "busName": "Bus Name",
    "notFound": "Not Found",
    "goodMorning": "Good Morning",
    "goodEvening": "Good Evening",
    "morningTrip": "Morning Trip",
    "isRequired": "Is Required",
    "addClassroom": "Add ClassRoom",
    "duplicatedUser": "Duplicated User",
    "addStudentToBus": "Add Student To Bus",
    "driverAdded": "Driver Added",
    "driverEdited": "Driver Edited",
    "eveningTrip": "Evening Trip",
    "fullDay": "Full Day",
    "busSubscription": "Bus Subscription",
    "studentAddress": "Student Address",
    "notRequired": "Not Required",
    "emailVerified": "Email Verified",
    "driverCurrentLocation": "Driver Current Location",
    "driveData": "Driver Data",
    "newAddress": "New Address",
    "supervisorCurrentLocation": "supervisor Current Location",
    "wrongCode": "Wrong Code",
    "absenceRequests": "absence Requests",
    "requestStatus": "Request Status",
    "newRequest": "New Request",
    "successfullyDone": "Successfully Done",
    "sonsNumber": "Sons Number",
    "notMatch": "Not Match",
    "extractExcel": "Extract Excel",
    "downloadPDF": "Download PDF",
    "supervisorEdited": "Supervisor Edited",
    "supervisorAdded": "Supervisor Added",
    "studentsAdded": "Students Added",
    "selectEduLev": "Selected Level",
    "classroomName": "Class Room Name",
    "studentsFile": "Students File",
    "studentEdited": "Student Edited",
    "studentAdded": "Student Added",
    "downloadExampleFile": "Download Example File",
    "checkInternetConnection": "Check your Internet Connection",
    "openTrips": "Open Trips",
    "tripTypes": "Trip Type",
    "supervisorName": "Supervisor Name",
    "old_address": "Old Address",
    "track": "Track",
    "wrongEmail": "Wrong Email?",
    "isEmailWrong": "Is the email address incorrect?",
    "contact_us": "Contact Us",
    "get_in_touch": "Get in Touch",
    "wed_love_to_hear": "We'd love to hear from you",
    "enter_your_name": "Enter your name",
    "enter_your_email": "Enter your email",
    "describe_problem": "Describe your problem or feedback",
    "please_enter_name": "Please enter your name",
    "please_enter_email": "Please enter your email",
    "please_valid_email": "Please enter a valid email",
    "please_describe_problem": "Please describe your problem",
    "message_too_long": "Message cannot be longer than 1000 characters",
    "contact_directly": "Or contact us directly:",
    "email_copied": "Email copied to clipboard",
    "sending": "Sending...",
    "email_sent": "Email sent successfully!",
    "failed_to_send": "Failed to send email: {error}",
     "Busaty - Parents": "Busaty - Parents",
    "Religion": "Religion",
    "NewAddress": "NewAddress",
    "select_absence_date": "Select Absence Date",
    "current_password": "Current Password",

    "addSon": "Add Son",
    "sonData": "Son Data",
    "showSonOnMap": "Show Son On The Map",
    "requestAbsence": "Absence",
    "date": "Date",
    "yourAddresses": "Your Addresses",
    "addManualAddress": "Add a manual address",
    "addAddressFromMap": "Add Address From Map",
    "addNewAddress": "Add New Address",
    "country": "Country",
    "nameSon": "Name Son",
    "nameParent": "Name Parent",
    "cityOrRegin": "City/Region",
    "streetName": "Street Name",
    "phase": "Phase",
    "addAddress": "Add Address",

    "stage": "Stage",
    "absenteeismRequests": "Absenteeism Requests",
    "searchingAddressOnMap": "Search Address On Map",
    "logout": "Logout",
    
    "settings": "Settings",

    "birth_date": "Birth Date",
    "grade_edu": "Grade",
    "school": "School",
    "add_new_request": "Add New Request",
    "choose_student": "Choose Student",
    "choose_trip_type": "Choose Trip Type",
    "location_on_map": "Location On Map",
    "send_successfully": "Your request sent successfully",
    "send_failed": "Failed, Make sure filling all data correctly",
    "send": "Send",

    "coupon": "Coupon",
    "enter_coupon": "Enter Coupon Code",
    "subscribe": "Subscribe",
    "benefits": "Benefits",
    "subscribe_with_coupon": "Subscribe With Coupon",
    "or": "OR",

    "no_trips": "There is no available trips right now!",
    "already_subscribed": "You are already subscribed",

    "trip_loading": "Loading trip, please wait...",
    "already_request":
        "You already have an address change request for this student",

    "all": "All",
    "unRead": "Un Read",
    "benefitBusatyBro": "Benefit Busay Bro",
    "withoutAds": "Without ads",
    "trackingSonInMoment":
        "Follow your son in real time while he is on the bus",
    "tackingBuysInMoment":
        "Follow the bus's movements while it is on its way to you",
    "deleteAccountTitle": "Delete Account Confirmation",
    "deleteAccountConfirm":
        "Are you sure you want to delete your account permanently?",
    "deleteAccountNote":
        "All your data will be deleted from our system automatically after 30 days",
    "deleteAccount": "Delete Account",
    "cancel": "Cancel",

    "terms_and_conditions": "Terms and Conditions",
    "subscriptionRequired": "Subscription Required",
    "subscriptionMessage": "Please subscribe to access this feature",
    "enter_coupon_description": "Enter your coupon code to subscribe to the premium service"
  };
  static const Map<String, Map<String, dynamic>> mapLocales = {
    "ar": ar,
    "en": en
  };
}
