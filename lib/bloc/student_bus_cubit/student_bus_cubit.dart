import 'package:bus/bloc/student_bus_cubit/student_bus_states.dart';
import 'package:bus/data/repo/student_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/student_models/student_model.dart';

class StudentBusCubit extends Cubit<StudentBusStates> {
  final _studentRepo = StudentRepo();
  List<StudentModel?>? studentModel;
  int? last_page, current_page;

  StudentBusCubit() : super(StudentBusInitialStates());

  static StudentBusCubit get(context) => BlocProvider.of(context);

  Future<void> getStudent({
    int? pageNumber,
  }) async {
    // Emit loading state before fetching data.
    emit(StudentBusLoadingStates());
    try {
      final response = await _studentRepo.repo(
        pageNumber: pageNumber,
      );

      if (response.status == true) {
        // If it's the first page or if no list exists, assign the fetched data directly.
        if (pageNumber == null || pageNumber == 1 || studentModel == null) {
          studentModel = response.studentModels;
        } else {
          // Otherwise, append the new data to the existing list.
          studentModel!.addAll(response.studentModels);
        }

        // Update the current and last page information.
        current_page = response.currentPage;
        last_page = response.lastPage;

        // Emit the success state with the updated list.
        emit(StudentBusSuccessStates(
          studentModel: studentModel,
          current_page: response.currentPage,
          last_page: response.lastPage,
        ));
      } else {
        emit(StudentBusErrorStates(error: "response.message"));
      }
    } catch (e, s) {
      debugPrint("catch error $s");
      debugPrint("catch error $e");
      emit(StudentBusErrorStates(error: e.toString()));
    }
  }

  Future<void> getEmptyBusStudents({
    int? withBusId,
    required int withoutBusId,
    int? classroomId,
    int page = 1,
  }) async {
    // Only emit loading state for first page
    if (page == 1) {
      emit(StudentBusLoadingStates(current_page: page));
    }

    try {
      final response = await _studentRepo.getEmptyBusStudents(
        withBusId: withBusId,
        withoutBusId: withoutBusId,
        classroomId: classroomId,
        page: page,
      );

      if (response.status) {
        // If it's the first page or if no list exists, assign the fetched data directly
        if (page == 1 || studentModel == null) {
          studentModel = response.studentModels;
        } else {
          // Otherwise, append the new data to the existing list
          studentModel!.addAll(response.studentModels);
        }

        // Update pagination information
        current_page = response.currentPage;
        last_page = response.lastPage;

        emit(StudentBusSuccessStates(
          studentModel: studentModel,
          current_page: response.currentPage,
          last_page: response.lastPage,
        ));
      } else {
        emit(
            StudentBusErrorStates(error: 'Failed to fetch empty bus students'));
      }
    } catch (e) {
      debugPrint("Error in getEmptyBusStudents: $e");
      emit(StudentBusErrorStates(error: e.toString()));
    }
  }
}
