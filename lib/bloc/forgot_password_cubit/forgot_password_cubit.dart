import 'package:bus/data/repo/auth_repo/forgot_password_repo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/global_variable.dart';
import '../../config/theme_colors.dart';
import '../../helper/cache_helper.dart';
import '../../views/custom_widgets/custom_text.dart';

part 'forgot_password_state.dart';

class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  ForgotPasswordCubit() : super(ForgotPasswordInitial());

  static ForgotPasswordCubit get(context)=> BlocProvider.of(context);
  final _forgotPasswordRepo = ForgotPasswordRepo();


  Future<void> forgotPassword({
    String? email,
  }) async {
    emit(ForgotPasswordLoading());
    try {
      final response = await _forgotPasswordRepo.forgotPasswordRepo(
        email: email
      );
      if (response.errors == false) {
        debugPrint(response.toString());
        const SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: "من فضلك راجع رسائل البريد من أجل الكود",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        emit(ForgotPasswordSuccess());
      } else {
        debugPrint("ErrorState: ${response.messages?.email?[0]}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.messages?.email?[0],
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e) {
      debugPrint("catch error $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
    emit(ForgotPasswordInitial());
  }

  /// reset password with cubit
  Future<void> resetPassword({
    String? email, code, password, passwordConfirmation
  }) async {
    emit(ForgotPasswordLoading());
    try {
      final response = await _forgotPasswordRepo.resetPasswordRepo(
        email: email,
        code: code,
        password: password,
        passwordConfirmation: passwordConfirmation
      );
      if (response.status?.status == 1) {
        debugPrint(response.toString());
        SnackBar snackBar = const SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: 'تم تغير كلمة السر بنجاح',
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        CacheHelper.putString("token", response.data!.token!);
        token = response.data!.token!;
        emit(ForgotPasswordSuccess());
      } else {
        debugPrint("ErrorState: ${response.status?.messages?.toJson()}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.status?.messages?.toJson(),
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e) {
      debugPrint("catch error $e");
      const SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "حدث خطأ حاول مرة أخرى",
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
    emit(ForgotPasswordInitial());
  }

}
