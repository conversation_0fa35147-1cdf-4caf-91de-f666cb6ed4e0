import 'package:bus/bloc/evening_trip_absences_cubit/evening_trip_absences_cubit.dart';
import 'package:bus/bloc/evening_trip_absences_cubit/evening_trip_absences_states.dart';
import 'package:bus/bloc/evening_trip_arrived_cubit/evening_trip_arrived_cubit.dart';
import 'package:bus/bloc/evening_trip_arrived_cubit/evening_trip_arrived_states.dart';
import 'package:bus/bloc/evening_trip_students_cubit/evening_trip_students_cubit.dart';
import 'package:bus/bloc/evening_trip_students_cubit/evening_trip_students_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/trip_models/evening_trip_absences_model.dart';
import 'package:bus/data/models/trip_models/evening_trip_arrived_model.dart';
import 'package:bus/data/models/trip_models/evening_trip_students_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class EveningTripScreen extends StatefulWidget {
  static const String routeName = PathRouteName.eveningTripScreen;
  final String name;
  final String busId;
  final String busName;
  final String? userableId;
  final String type;
  final dynamic tripData;

  const EveningTripScreen({
    super.key,
    required this.busId,
    required this.busName,
    required this.type,
    this.userableId,
    required this.name,
    required this.tripData,
  });

  @override
  State<EveningTripScreen> createState() => _EveningTripScreenState();
}

class _EveningTripScreenState extends State<EveningTripScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  GoogleMapController? mapController;
  Set<Marker> markers = {};
  LatLng? currentLocation;

  // Cubits for fetching students data
  late EveningTripStudentsCubit _eveningTripStudentsCubit;
  late EveningTripArrivedCubit _eveningTripArrivedCubit;
  late EveningTripAbsencesCubit _eveningTripAbsencesCubit;

  // Lists for students data
  List<EveningTripPresentStudent> _inBusStudents = [];
  List<EveningTripArrivedStudent> _arrivedHomeStudents = [];
  List<EveningTripAbsentStudent> _absentStudents = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize Cubits
    _initCubits();

    // Fetch students data for this trip
    _fetchInBusStudents();
    _fetchArrivedHomeStudents();
    _fetchAbsentStudents();

    // Initialize with default location or get from tripData if available
    if (widget.tripData != null &&
        widget.tripData.latitude != null &&
        widget.tripData.longitude != null) {
      try {
        double lat = double.parse(widget.tripData.latitude ?? "0.0");
        double lng = double.parse(widget.tripData.longitude ?? "0.0");
        currentLocation = LatLng(lat, lng);

        markers.add(
          Marker(
            markerId: const MarkerId('busLocation'),
            position: currentLocation!,
            infoWindow: InfoWindow(
              title: widget.busName,
              snippet: AppStrings.eveningTrip.tr(),
            ),
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueOrange),
          ),
        );
      } catch (e) {
        debugPrint("Error parsing coordinates: $e");
      }
    }
  }

  // Fetch students in the bus from the API
  Future<void> _fetchInBusStudents() async {
    try {
      await _eveningTripStudentsCubit.getPresentStudents(
          tripId: widget.tripData?.id?.toString() ?? widget.busId);
      setState(() {
        _inBusStudents = _eveningTripStudentsCubit.presentStudents;
      });
    } catch (e) {
      debugPrint("Error fetching students in bus: $e");
    }
  }

  // Fetch students who arrived home from the API
  Future<void> _fetchArrivedHomeStudents() async {
    try {
      await _eveningTripArrivedCubit.getArrivedStudents(busId: widget.busId);
      setState(() {
        _arrivedHomeStudents = _eveningTripArrivedCubit.arrivedStudents;
      });
    } catch (e) {
      debugPrint("Error fetching students who arrived home: $e");
    }
  }

  // Fetch absent students from the API
  Future<void> _fetchAbsentStudents() async {
    try {
      await _eveningTripAbsencesCubit.getAbsentStudents(busId: widget.busId);
      setState(() {
        _absentStudents = _eveningTripAbsencesCubit.absentStudents;
      });
    } catch (e) {
      debugPrint("Error fetching absent students: $e");
    }
  }

  // Initialize Cubits
  void _initCubits() {
    _eveningTripStudentsCubit = EveningTripStudentsCubit();
    _eveningTripArrivedCubit = EveningTripArrivedCubit();
    _eveningTripAbsencesCubit = EveningTripAbsencesCubit();
  }

  // Refresh all data for the evening trip
  Future<void> _refreshEveningTripData() async {
    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('جاري تحديث البيانات...'),
        duration: const Duration(seconds: 1),
      ),
    );

    // Close existing Cubits
    _eveningTripStudentsCubit.close();
    _eveningTripArrivedCubit.close();
    _eveningTripAbsencesCubit.close();

    // Re-initialize Cubits
    _initCubits();

    // Fetch all data
    await Future.wait([
      _fetchInBusStudents(),
      _fetchArrivedHomeStudents(),
      _fetchAbsentStudents(),
    ]);

    setState(() {});

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم تحديث البيانات بنجاح'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    mapController?.dispose();

    // Close Cubits
    _eveningTripStudentsCubit.close();
    _eveningTripArrivedCubit.close();
    _eveningTripAbsencesCubit.close();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: IconButton(
          icon: Icon(
            Icons.refresh,
            color: TColor.white,
            size: 24.w,
          ),
          onPressed: () {
            _refreshEveningTripData();
          },
        ),
        titleWidget: Row(
          children: [
            CustomText(
              text: "${AppStrings.eveningTrip.tr()} - ${widget.name}",
              fontSize: 18,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: InkWell(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            context.locale.toString() == "ar"
                ? AppAssets.arrowBack
                : AppAssets.forwardArrow,
            colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
        ),
      ),
      body: Column(
        children: [
          // Trip Info Card
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(AppStrings.name.tr(), widget.name),
                      SizedBox(height: 5.h),
                      _buildInfoRow(AppStrings.bus_name.tr(), widget.busName),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(
                          tr('Trip Type'), AppStrings.eveningTrip.tr()),
                      SizedBox(height: 5.h),
                      _buildInfoRow(tr('Status'), tr('Active')),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(30.r),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: TColor.dialogName,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(30.r),
                color: TColor.mainColor,
              ),
              labelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              tabs: [
                Tab(text: tr('In Bus')),
                Tab(text: tr('Arrived Home')),
                Tab(text: tr('Absent')),
              ],
              padding: EdgeInsets.all(4.w),
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // In Bus Students Tab
                _buildInBusStudentsTab(),

                // Arrived Home Students Tab
                _buildArrivedHomeStudentsTab(),

                // Absent Students Tab
                _buildAbsentStudentsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInBusStudentsTab() {
    return BlocProvider(
      create: (context) => _eveningTripStudentsCubit,
      child: BlocBuilder<EveningTripStudentsCubit, EveningTripStudentsState>(
        builder: (context, state) {
          if (state is EveningTripStudentsLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is EveningTripStudentsErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 70.w,
                    color: Colors.red,
                  ),
                  SizedBox(height: 20.h),
                  CustomText(
                    text: state.error,
                    fontSize: 16,
                    color: Colors.red,
                    fontW: FontWeight.w500,
                  ),
                ],
              ),
            );
          }

          return Container(
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: _inBusStudents.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.directions_bus_outlined,
                          size: 70.w,
                          color: TColor.grey5,
                        ),
                        SizedBox(height: 20.h),
                        CustomText(
                          text: tr('No students in bus'),
                          fontSize: 18,
                          color: TColor.grey5,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: _inBusStudents.length,
                    padding: EdgeInsets.all(16.w),
                    separatorBuilder: (context, index) => Divider(
                      color: TColor.mainColor.withOpacity(0.2),
                      height: 1,
                    ),
                    itemBuilder: (context, index) {
                      final student = _inBusStudents[index];
                      // Get student info from the nested students object
                      final studentInfo = student.students;
                      // Get grade name from grade_id (would need a lookup in a real app)
                      String gradeName = "الصف ${studentInfo?.gradeId}";

                      return _buildStudentItem(
                        studentInfo?.name ?? tr('Unknown'),
                        gradeName,
                        tr('In Bus'),
                        Colors.blue,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildArrivedHomeStudentsTab() {
    return BlocProvider(
      create: (context) => _eveningTripArrivedCubit,
      child: BlocBuilder<EveningTripArrivedCubit, EveningTripArrivedState>(
        builder: (context, state) {
          if (state is EveningTripArrivedLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is EveningTripArrivedErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 70.w,
                    color: Colors.red,
                  ),
                  SizedBox(height: 20.h),
                  CustomText(
                    text: state.error,
                    fontSize: 16,
                    color: Colors.red,
                    fontW: FontWeight.w500,
                  ),
                ],
              ),
            );
          }

          return Container(
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: _arrivedHomeStudents.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.home_outlined,
                          size: 70.w,
                          color: TColor.grey5,
                        ),
                        SizedBox(height: 20.h),
                        CustomText(
                          text: tr('No students arrived home'),
                          fontSize: 18,
                          color: TColor.grey5,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: _arrivedHomeStudents.length,
                    padding: EdgeInsets.all(16.w),
                    separatorBuilder: (context, index) => Divider(
                      color: TColor.mainColor.withOpacity(0.2),
                      height: 1,
                    ),
                    itemBuilder: (context, index) {
                      final student = _arrivedHomeStudents[index];
                      // Get grade name from grade_id (would need a lookup in a real app)
                      String gradeName = "الصف ${student.gradeId}";

                      return _buildStudentItem(
                        student.name ?? tr('Unknown'),
                        gradeName,
                        tr('Arrived Home'),
                        Colors.green,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildAbsentStudentsTab() {
    return BlocProvider(
      create: (context) => _eveningTripAbsencesCubit,
      child: BlocBuilder<EveningTripAbsencesCubit, EveningTripAbsencesState>(
        builder: (context, state) {
          if (state is EveningTripAbsencesLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is EveningTripAbsencesErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 70.w,
                    color: Colors.red,
                  ),
                  SizedBox(height: 20.h),
                  CustomText(
                    text: state.error,
                    fontSize: 16,
                    color: Colors.red,
                    fontW: FontWeight.w500,
                  ),
                ],
              ),
            );
          }

          return Container(
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: _absentStudents.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.person_off_outlined,
                          size: 70.w,
                          color: TColor.grey5,
                        ),
                        SizedBox(height: 20.h),
                        CustomText(
                          text: tr('No students absent'),
                          fontSize: 18,
                          color: TColor.grey5,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: _absentStudents.length,
                    padding: EdgeInsets.all(16.w),
                    separatorBuilder: (context, index) => Divider(
                      color: TColor.mainColor.withOpacity(0.2),
                      height: 1,
                    ),
                    itemBuilder: (context, index) {
                      final student = _absentStudents[index];
                      // Get grade name from grade_id (would need a lookup in a real app)
                      String gradeName = "الصف ${student.gradeId}";

                      return _buildStudentItem(
                        student.name ?? tr('Unknown'),
                        gradeName,
                        tr('Absent'),
                        Colors.red,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildStudentItem(
      String name, String grade, String status, Color statusColor) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        children: [
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              color: TColor.mainColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.person,
                color: TColor.mainColor,
                size: 30.w,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: TColor.titleColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  grade,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: TColor.grey5,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Text(
              status,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: TColor.mainColor,
            fontSize: 14.sp,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: TColor.titleColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
