import 'package:bus/bloc/morning_trip_absences_cubit/morning_trip_absences_cubit.dart';
import 'package:bus/bloc/morning_trip_absences_cubit/morning_trip_absences_states.dart';
import 'package:bus/bloc/morning_trip_students_cubit/morning_trip_students_cubit.dart';
import 'package:bus/bloc/morning_trip_students_cubit/morning_trip_students_states.dart';
import 'package:bus/bloc/morning_trip_waiting_cubit/morning_trip_waiting_cubit.dart';
import 'package:bus/bloc/morning_trip_waiting_cubit/morning_trip_waiting_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/trip_models/morning_trip_absences_model.dart';
import 'package:bus/data/models/trip_models/morning_trip_students_model.dart';
import 'package:bus/data/models/trip_models/morning_trip_waiting_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MorningTripScreen extends StatefulWidget {
  static const String routeName = PathRouteName.morningTripScreen;
  final String name;
  final String busId;
  final String busName;
  final String? userableId;
  final String type;
  final dynamic tripData;

  const MorningTripScreen({
    super.key,
    required this.busId,
    required this.busName,
    required this.type,
    this.userableId,
    required this.name,
    required this.tripData,
  });

  @override
  State<MorningTripScreen> createState() => _MorningTripScreenState();
}

class _MorningTripScreenState extends State<MorningTripScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  GoogleMapController? mapController;
  Set<Marker> markers = {};
  LatLng? currentLocation;

  // Cubits for fetching students data
  late MorningTripStudentsCubit _morningTripStudentsCubit;
  late MorningTripAbsencesCubit _morningTripAbsencesCubit;
  late MorningTripWaitingCubit _morningTripWaitingCubit;

  // Lists for students data
  List<MorningTripStudent> _presentStudents = [];
  List<MorningTripAbsentStudent> _absentStudents = [];
  List<MorningTripWaitingStudent> _waitingStudents = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize Cubits
    _initCubits();

    // Fetch students data for this bus
    _fetchPresentStudents();
    _fetchAbsentStudents();
    _fetchWaitingStudents();

    // Initialize with default location or get from tripData if available
    if (widget.tripData != null &&
        widget.tripData.latitude != null &&
        widget.tripData.longitude != null) {
      try {
        double lat = double.parse(widget.tripData.latitude ?? "0.0");
        double lng = double.parse(widget.tripData.longitude ?? "0.0");
        currentLocation = LatLng(lat, lng);

        markers.add(
          Marker(
            markerId: const MarkerId('busLocation'),
            position: currentLocation!,
            infoWindow: InfoWindow(
              title: widget.busName,
              snippet: AppStrings.morningTrip.tr(),
            ),
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          ),
        );
      } catch (e) {
        debugPrint("Error parsing coordinates: $e");
      }
    }
  }

  // Fetch present students from the API
  Future<void> _fetchPresentStudents() async {
    try {
      await _morningTripStudentsCubit.getPresentStudents(busId: widget.busId);
      setState(() {
        _presentStudents = _morningTripStudentsCubit.presentStudents;
      });
    } catch (e) {
      debugPrint("Error fetching present students: $e");
    }
  }

  // Fetch absent students from the API
  Future<void> _fetchAbsentStudents() async {
    try {
      await _morningTripAbsencesCubit.getAbsentStudents(busId: widget.busId);
      setState(() {
        _absentStudents = _morningTripAbsencesCubit.absentStudents;
      });
    } catch (e) {
      debugPrint("Error fetching absent students: $e");
    }
  }

  // Fetch waiting students from the API
  Future<void> _fetchWaitingStudents() async {
    try {
      await _morningTripWaitingCubit.getWaitingStudents(busId: widget.busId);
      setState(() {
        _waitingStudents = _morningTripWaitingCubit.waitingStudents;
      });
    } catch (e) {
      debugPrint("Error fetching waiting students: $e");
    }
  }

  // Initialize Cubits
  void _initCubits() {
    _morningTripStudentsCubit = MorningTripStudentsCubit();
    _morningTripAbsencesCubit = MorningTripAbsencesCubit();
    _morningTripWaitingCubit = MorningTripWaitingCubit();
  }

  // Refresh all data for the morning trip
  Future<void> _refreshMorningTripData() async {
    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('جاري تحديث البيانات...'),
        duration: const Duration(seconds: 1),
      ),
    );

    // Close existing Cubits
    _morningTripStudentsCubit.close();
    _morningTripAbsencesCubit.close();
    _morningTripWaitingCubit.close();

    // Re-initialize Cubits
    _initCubits();

    // Fetch all data
    await Future.wait([
      _fetchWaitingStudents(),
      _fetchPresentStudents(),
      _fetchAbsentStudents(),
    ]);

    setState(() {});

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث البيانات بنجاح'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    mapController?.dispose();

    // Close Cubits
    _morningTripStudentsCubit.close();
    _morningTripAbsencesCubit.close();
    _morningTripWaitingCubit.close();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: IconButton(
          icon: Icon(
            Icons.refresh,
            color: TColor.white,
            size: 24.w,
          ),
          onPressed: () {
            _refreshMorningTripData();
          },
        ),
        titleWidget: Row(
          children: [
            CustomText(
              text: "${AppStrings.morningTrip.tr()} - ${widget.name}",
              fontSize: 18,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: InkWell(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            context.locale.toString() == "ar"
                ? AppAssets.arrowBack
                : AppAssets.forwardArrow,
            colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
            width: 25.w,
            height: 25.w,
          ),
        ),
      ),
      body: Column(
        children: [
          // Trip Info Card
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(AppStrings.name.tr(), widget.name),
                      SizedBox(height: 5.h),
                      _buildInfoRow(AppStrings.bus_name.tr(), widget.busName),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoRow(
                          tr('Trip Type'), AppStrings.morningTrip.tr()),
                      SizedBox(height: 5.h),
                      _buildInfoRow(tr('Status'), tr('Active')),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(30.r),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: TColor.dialogName,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(30.r),
                color: TColor.mainColor,
              ),
              labelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              tabs: [
                Tab(text: tr('Waiting')),
                Tab(text: tr('Present')),
                Tab(text: tr('Absent')),
              ],
              padding: EdgeInsets.all(4.w),
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Waiting Students Tab
                _buildWaitingStudentsTab(),

                // Present Students Tab
                _buildPresentStudentsTab(),

                // Absent Students Tab
                _buildAbsentStudentsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWaitingStudentsTab() {
    return BlocProvider(
      create: (context) => _morningTripWaitingCubit,
      child: BlocBuilder<MorningTripWaitingCubit, MorningTripWaitingState>(
        builder: (context, state) {
          if (state is MorningTripWaitingLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MorningTripWaitingErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 70.w,
                    color: Colors.red,
                  ),
                  SizedBox(height: 20.h),
                  CustomText(
                    text: state.error,
                    fontSize: 16,
                    color: Colors.red,
                    fontW: FontWeight.w500,
                  ),
                ],
              ),
            );
          }

          return Container(
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: _waitingStudents.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.hourglass_empty,
                          size: 70.w,
                          color: TColor.grey5,
                        ),
                        SizedBox(height: 20.h),
                        CustomText(
                          text: tr('No students waiting'),
                          fontSize: 18,
                          color: TColor.grey5,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: _waitingStudents.length,
                    padding: EdgeInsets.all(16.w),
                    separatorBuilder: (context, index) => Divider(
                      color: TColor.mainColor.withOpacity(0.2),
                      height: 1,
                    ),
                    itemBuilder: (context, index) {
                      final student = _waitingStudents[index];
                      // Get grade name from grade_id (would need a lookup in a real app)
                      String gradeName = "الصف ${student.gradeId}";

                      return _buildStudentItem(
                        student.name ?? tr('Unknown'),
                        gradeName,
                        tr('Waiting'),
                        Colors.orange,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildPresentStudentsTab() {
    return BlocProvider(
      create: (context) => _morningTripStudentsCubit,
      child: BlocBuilder<MorningTripStudentsCubit, MorningTripStudentsState>(
        builder: (context, state) {
          if (state is MorningTripStudentsLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MorningTripStudentsErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 70.w,
                    color: Colors.red,
                  ),
                  SizedBox(height: 20.h),
                  CustomText(
                    text: state.error,
                    fontSize: 16,
                    color: Colors.red,
                    fontW: FontWeight.w500,
                  ),
                ],
              ),
            );
          }

          return Container(
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: _presentStudents.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 70.w,
                          color: TColor.grey5,
                        ),
                        SizedBox(height: 20.h),
                        CustomText(
                          text: tr('No students present'),
                          fontSize: 18,
                          color: TColor.grey5,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: _presentStudents.length,
                    padding: EdgeInsets.all(16.w),
                    separatorBuilder: (context, index) => Divider(
                      color: TColor.mainColor.withOpacity(0.2),
                      height: 1,
                    ),
                    itemBuilder: (context, index) {
                      final student = _presentStudents[index];
                      // Get grade name from grade_id (would need a lookup in a real app)
                      String gradeName = "الصف ${student.gradeId}";

                      return _buildStudentItem(
                        student.name ?? tr('Unknown'),
                        gradeName,
                        tr('Present'),
                        Colors.green,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildAbsentStudentsTab() {
    return BlocProvider(
      create: (context) => _morningTripAbsencesCubit,
      child: BlocBuilder<MorningTripAbsencesCubit, MorningTripAbsencesState>(
        builder: (context, state) {
          if (state is MorningTripAbsencesLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MorningTripAbsencesErrorState) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 70.w,
                    color: Colors.red,
                  ),
                  SizedBox(height: 20.h),
                  CustomText(
                    text: state.error,
                    fontSize: 16,
                    color: Colors.red,
                    fontW: FontWeight.w500,
                  ),
                ],
              ),
            );
          }

          return Container(
            margin: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: _absentStudents.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.person_off_outlined,
                          size: 70.w,
                          color: TColor.grey5,
                        ),
                        SizedBox(height: 20.h),
                        CustomText(
                          text: tr('No students absent'),
                          fontSize: 18,
                          color: TColor.grey5,
                          fontW: FontWeight.w500,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: _absentStudents.length,
                    padding: EdgeInsets.all(16.w),
                    separatorBuilder: (context, index) => Divider(
                      color: TColor.mainColor.withOpacity(0.2),
                      height: 1,
                    ),
                    itemBuilder: (context, index) {
                      final student = _absentStudents[index];
                      // Absent students don't have grade info in the API response
                      // So we'll just show "غائب" as the grade
                      return _buildStudentItem(
                        student.name ?? tr('Unknown'),
                        tr('Absent Student'),
                        tr('Absent'),
                        Colors.red,
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  Widget _buildStudentItem(
      String name, String grade, String status, Color statusColor) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        children: [
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              color: TColor.mainColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.person,
                color: TColor.mainColor,
                size: 30.w,
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: TColor.titleColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  grade,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: TColor.grey5,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Text(
              status,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: TColor.mainColor,
            fontSize: 14.sp,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: TColor.titleColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
