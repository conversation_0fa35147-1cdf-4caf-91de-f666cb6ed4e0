import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_cubit.dart';
import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_states.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_drop_down_button.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:logger/logger.dart'; // Import the logger package.
import 'package:bus/bloc/student_bus_cubit/student_bus_cubit.dart';
import 'package:bus/bloc/student_bus_cubit/student_bus_states.dart';
import 'package:bus/bloc/student_cubit/student_filter_cubit.dart';
import 'package:bus/bloc/student_cubit/student_filter_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_widgets/custom_search_w.dart';
import 'package:bus/translations/local_keys.g.dart';

final Logger logger = Logger();

class TextToSpeechService {
  static const MethodChannel _channel = MethodChannel('com.example.tts');

  Future<void> speak(String text, {String language = "en-US"}) async {
    try {
      await _channel
          .invokeMethod('speak', {"text": text, "language": language});
      logger.i("TTS: Speaking text '$text' in language $language");
    } on PlatformException catch (e) {
      logger.e("TTS Error: ${e.message}");
    } catch (e) {
      logger.e("TTS Unknown Error: $e");
    }
  }

  Future<void> stop() async {
    try {
      await _channel.invokeMethod('stop');
      logger.i("TTS: Stop called successfully");
    } on PlatformException catch (e) {
      logger.e("TTS Stop Error: ${e.message}");
    } catch (e) {
      logger.e("TTS Stop Unknown Error: $e");
    }
  }
}


class FadeInWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  const FadeInWidget(
      {super.key,
      required this.child,
      this.duration = const Duration(milliseconds: 500)});

  @override
  State<FadeInWidget> createState() => _FadeInWidgetState();
}

class _FadeInWidgetState extends State<FadeInWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: widget.child,
    );
  }
}


class StudentToBusScreen extends StatefulWidget {
  static const String routeName = PathRouteName.student;
  final String busId;
  final String? busName;
  const StudentToBusScreen({super.key, this.busId = '', required this.busName});

  @override
  State<StudentToBusScreen> createState() => _StudentToBusScreenState();
}

class _StudentToBusScreenState extends State<StudentToBusScreen>
    with TickerProviderStateMixin {
  AnimationController? controller;
  AnimationController? controllerIcon;
  Animation<double>? opacity;
  TextEditingController? cSearchStudentName;

  List<StudentModel?> studentsToAdd = [];
  List<StudentModel> selectedStudents = [];
  bool searchCheckFilter = false;
  bool _isLoadingMore = false;
  late ScrollController _scrollController;
  int? selectedBusId;

  final TextToSpeechService _ttsService = TextToSpeechService();

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: Colors.grey[800]),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: children,
        ),
      ],
    );
  }

  // Helper method to build info chips
  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: color.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    cSearchStudentName = TextEditingController();
    cSearchStudentName!.addListener(() {
      if (cSearchStudentName!.text.isEmpty) {
        searchCheckFilter = false;
        // Load empty bus students when search is cleared
        _loadEmptyBusStudents();
      }
      setState(() {});
    });
    controller = AnimationController(
        duration: const Duration(milliseconds: 200), vsync: this);
    controllerIcon = AnimationController(
        duration: const Duration(milliseconds: 500), vsync: this);
    opacity = Tween<double>(begin: 0.0, end: 1.0).animate(controller!);

    // Initialize scroll controller and add listener for pagination
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // Load initial data with bus filter
    _loadEmptyBusStudents();

    // Load available buses
    context.read<BusesCubit>().getAllBuses();

    logger.i("StudentToBusScreen initialized");
  }

  // Load empty bus students with current page
  Future<void> _loadEmptyBusStudents({int page = 1}) {
    return context.read<StudentBusCubit>().getEmptyBusStudents(
          withoutBusId: int.parse(widget.busId),
          withBusId: selectedBusId,
          page: page,
        );
  }

  // Handle scroll for pagination
  void _onScroll() async {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      if (!searchCheckFilter) {
        final state = context.read<StudentBusCubit>().state;
        if (state is StudentBusSuccessStates && !_isLoadingMore) {
          if (state.current_page != null &&
              state.last_page != null &&
              state.current_page! < state.last_page!) {
            setState(() {
              _isLoadingMore = true;
            });
            await _loadEmptyBusStudents(page: state.current_page! + 1);
            setState(() {
              _isLoadingMore = false;
            });
          }
        }
      }
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    controllerIcon?.dispose();
    cSearchStudentName?.dispose();
    _scrollController.dispose();
    logger.i("StudentToBusScreen disposed");
    super.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) super.setState(fn);
  }

  void toggleAnimation() {
    if (controller?.status == AnimationStatus.dismissed) {
      controller?.forward();
    } else if (controller?.status == AnimationStatus.completed) {
      controller?.reverse();
    }
  }

  void toggleIcon() {
    if (controllerIcon?.status == AnimationStatus.dismissed) {
      controllerIcon?.forward();
    } else if (controllerIcon?.status == AnimationStatus.completed) {
      controllerIcon?.reverse();
    }
  }

  Widget _buildBusFilter() {
    return BlocBuilder<BusesCubit, BusesState>(
      builder: (context, state) {
        if (state is BusesLoadingStates) {
          return const Center(
            child: CircularProgressIndicator(color: TColor.mainColor),
          );
        } else if (state is BusesSuccessStates) {
          List<BusesInfoModel> buses = [
            BusesInfoModel(id: 0, name: AppStrings.selectBus.tr()),
            ...?state.busesDataModels?.data,
          ];
          return CustomDropDownButton(
            items: buses
                .map((value) => DropdownMenuItem<int>(
                      value: value.id,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Text(value.name ?? ''),
                      ),
                    ))
                .toList(),
            onChanged: (v) {
              setState(() {
                selectedBusId = v == 0 ? null : v;
                _loadEmptyBusStudents(page: 1);
              });
            },
            value: selectedBusId ?? 0,
          );
        }
        return const SizedBox();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => BusesCubit()..getAllBuses()),
        ],
        child: Scaffold(
          bottomNavigationBar: Padding(
            padding: const EdgeInsets.all(8.0),
            child: SizedBox(
              height: 50,
              width: 120,
              child: BlocConsumer<AddRemoveStudentToBusCubit,
                  AddRemoveStudentToBusStates>(
                listener: (context, state) {
                  if (state is AddRemoveStudentToBusSuccessStates) {
                    logger.i("Students successfully added to bus");
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        backgroundColor: TColor.greenSuccess,
                        content: CustomText(
                          text: AppStrings.studentsAdded.tr(),
                          fontSize: 18,
                          maxLine: 5,
                          color: TColor.white,
                        ),
                      ),
                    );
                  } else if (state is AddRemoveStudentToBusErrorStates) {
                    logger.e("Error adding students to bus");
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        backgroundColor: TColor.redAccent,
                        content: CustomText(
                          text: "No Students added",
                          fontSize: 18,
                          maxLine: 5,
                          color: TColor.white,
                        ),
                      ),
                    );
                  }
                },
                builder: (context, state) {
                  if (state is! AddRemoveStudentToBusLoadingStates) {
                    return CustomButton(
                      bgColor: Colors.green,
                      borderColor: Colors.green,
                      text: AppStrings.add.tr(),
                      fontSize: 22,
                      onTap: () async {
                        try {
                          List<String> studentIds =
                              selectedStudents.map((e) => e.id!).toList();
                          logger.i(
                              "Attempting to add ${studentIds.length} students to bus ${widget.busId}");
                          context
                              .read<AddRemoveStudentToBusCubit>()
                              .addStudents(
                                studentIds: studentIds,
                                busId: widget.busId,
                              );
                        } catch (e) {
                          logger.e("Error in Add button onTap: $e");
                        }
                      },
                    );
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                },
              ),
            ),
          ),
          backgroundColor: const Color(0xFFF8F9FC),
          appBar: CustomAppBar(
            titleWidget: CustomText(
              text: '${AppStrings.addStudentToBus.tr()} (${widget.busName})',
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            leftWidget: InkWell(
              onTap: () {
                logger.i("Navigating back from StudentToBusScreen");
                Navigator.pop(context);
              },
              child: SvgPicture.asset(
                context.locale.toString() == "ar"
                    ? AppAssets.arrowBack
                    : AppAssets.forwardArrow,
                colorFilter:
                    const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                width: 25.w,
                height: 25.w,
              ),
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          margin: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(
                                          color: Colors.grey.shade100)),
                                ),
                                child: _buildBusFilter(),
                              ),
                            ],
                          ),
                        ),
                        // Static header (e.g. search widget)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border:
                                      Border.all(color: Colors.grey.shade200),
                                ),
                                child: CustomSearchW(
                                  controller: cSearchStudentName,
                                  onpressedSearch: () {
                                    try {
                                      FocusScope.of(context).unfocus();
                                      context
                                          .read<StudentFilterCubit>()
                                          .getStudentWithFilters(
                                            studentName:
                                                cSearchStudentName?.text ?? "",
                                          );
                                      searchCheckFilter = true;
                                      setState(() {});

                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content: Text(
                                            'Searching for "${cSearchStudentName?.text}"'),
                                        duration: const Duration(seconds: 1),
                                        backgroundColor: Colors.black87,
                                        behavior: SnackBarBehavior.floating,
                                        margin: const EdgeInsets.all(10),
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                      ));
                                    } catch (e) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content: Text('Search error: $e'),
                                        backgroundColor: Colors.red.shade400,
                                        behavior: SnackBarBehavior.floating,
                                        margin: const EdgeInsets.all(10),
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                      ));
                                    }
                                  },
                                  hintText: AppStrings.searchStudentHint.tr(),
                                  type: "student",
                                  isSearch: true,
                                  isIcon: false,
                                ),
                              ),
                              if (searchCheckFilter &&
                                  cSearchStudentName?.text.isNotEmpty == true)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: Row(
                                    children: [
                                      Icon(Icons.info_outline,
                                          size: 16,
                                          color: Colors.grey.shade600),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Showing results for "${cSearchStudentName?.text}"',
                                        style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 12),
                                      ),
                                      const Spacer(),
                                      TextButton(
                                        onPressed: () {
                                          cSearchStudentName?.clear();
                                          searchCheckFilter = false;
                                          setState(() {});
                                          _loadEmptyBusStudents();
                                        },
                                        style: TextButton.styleFrom(
                                            padding: EdgeInsets.zero),
                                        child: const Text('Clear',
                                            style: TextStyle(fontSize: 12)),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                        const SBox(h: 20),
                        // Dynamic list area
                        searchCheckFilter
                            ? BlocBuilder<StudentFilterCubit,
                                StudentFilterState>(
                                builder: (context, state) {
                                  if (state is StudentFilterLoadingStates) {
                                    logger.i("StudentFilterCubit: Loading");
                                    return Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const CircularProgressIndicator(
                                              color: TColor.mainColor),
                                          const SizedBox(height: 20),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 24, vertical: 12),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(30),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.05),
                                                  blurRadius: 10,
                                                  offset: const Offset(0, 4),
                                                ),
                                              ],
                                            ),
                                            child: Text(
                                              'Loading students...',
                                              style: TextStyle(
                                                  color: Colors.grey.shade600),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else if (state
                                      is StudentFilterSuccessStates) {
                                    studentsToAdd = state.searchData
                                        .where((element) =>
                                            element!.bus_id.toString() !=
                                            widget.busId)
                                        .toList();
                                    if (studentsToAdd.isEmpty) {
                                      logger.w(
                                          "No students found in search results");
                                      return Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(20),
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade50,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                Icons.school_outlined,
                                                size: 48,
                                                color: Colors.grey.shade400,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            CustomText(
                                              text: AppStrings.studentNotFound
                                                  .tr(),
                                              fontSize: 16,
                                              fontW: FontWeight.w500,
                                              color: Colors.grey.shade600,
                                            ),
                                            const SizedBox(height: 8),
                                            TextButton.icon(
                                              onPressed: () =>
                                                  _loadEmptyBusStudents(),
                                              icon: const Icon(Icons.refresh),
                                              label: const Text('Refresh'),
                                              style: TextButton.styleFrom(
                                                foregroundColor:
                                                    TColor.mainColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }
                                    logger.i(
                                        "StudentFilterCubit: Loaded ${studentsToAdd.length} students");
                                    return Container(
                                      height: MediaQuery.of(context).size.height * 0.5, // Use 50% of screen height
                                      child: ListView.builder(
                                        controller: _scrollController,
                                        itemCount: studentsToAdd.length,
                                        itemBuilder: (context, index) {
                                        final student = studentsToAdd[index];
                                          return AnimatedContainer(
                                            duration: const Duration(
                                                milliseconds: 300),
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 16, vertical: 10),
                                            decoration: BoxDecoration(
                                              color: selectedStudents.any((s) =>
                                                      s.id == student!.id)
                                                  ? Colors.green.shade50.withOpacity(0.9)
                                                  : Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: selectedStudents.any(
                                                          (s) =>
                                                              s.id ==
                                                              student!.id)
                                                      ? Colors.green
                                                          .withOpacity(0.15)
                                                      : Colors.black
                                                          .withOpacity(0.08),
                                                  blurRadius: 15,
                                                  spreadRadius: 1,
                                                  offset: const Offset(0, 5),
                                                ),
                                              ],
                                            ),
                                            child: Material(
                                                color: Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                clipBehavior: Clip.antiAlias,
                                                child: ExpansionTile(
                                                  expandedCrossAxisAlignment: CrossAxisAlignment.start,
                                                  tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                                  leading: Hero(
                                                    tag:
                                                        'avatar_${student?.id ?? ''}',
                                                    child: Container(
                                                      width: 56,
                                                      height: 56,
                                                      decoration: BoxDecoration(
                                                        gradient:
                                                            LinearGradient(
                                                          colors: selectedStudents
                                                                  .any((s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)
                                                              ? [
                                                                  Colors.green
                                                                      .shade400,
                                                                  Colors.green
                                                                      .shade600,
                                                                ]
                                                              : [
                                                                  Colors.blue
                                                                      .shade400,
                                                                  Colors.blue
                                                                      .shade600,
                                                                ],
                                                          begin:
                                                              Alignment.topLeft,
                                                          end: Alignment
                                                              .bottomRight,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(24),
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: (selectedStudents.any((s) =>
                                                                        s.id ==
                                                                        student!
                                                                            .id)
                                                                    ? Colors
                                                                        .green
                                                                    : Colors
                                                                        .blue)
                                                                .withOpacity(
                                                                    0.2),
                                                            blurRadius: 10,
                                                            spreadRadius: 1,
                                                            offset:
                                                                const Offset(
                                                                    0, 4),
                                                          ),
                                                        ],
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          (student?.name ?? '?')
                                                              .substring(0, 1)
                                                              .toUpperCase(),
                                                          style: const TextStyle(
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            color: Colors.white,
                                                            fontSize: 26,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  title: Text(
                                                    student?.name ?? "",
                                                    style: TextStyle(
                                                        fontSize: 17,
                                                        fontWeight: FontWeight.w600,
                                                        color: selectedStudents.any((s) => s.id == student!.id)
                                                            ? Colors.green.shade700
                                                            : Colors.grey.shade900),
                                                  ),
                                                  // Use a Column to show multiple lines if class or bus name exist.
                                                  subtitle: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      // Personal Information Section (معلومات شخصية)
                                                      _buildSection(
                                                        'معلومات شخصية',
                                                        Icons.person_outline,
                                                        [
                                                          if (student?.phone != null)
                                                            _buildInfoChip(
                                                              Icons.phone_outlined,
                                                              'رقم الهاتف: ${student?.name}',
                                                              Colors.blue,
                                                            ),
                                                          if (student?.Date_Birth != null)
                                                            _buildInfoChip(
                                                              Icons.cake_outlined,
                                                              'تاريخ الميلاد: ${student?.Date_Birth}',
                                                              Colors.purple,
                                                            ),
                                                          if (student?.gender_id != null)
                                                            _buildInfoChip(
                                                              Icons.wc_outlined,
                                                              'الجنس: ${student?.gender_id}',
                                                              Colors.pink,
                                                            ),
                                                          if (student?.type__blood_id != null)
                                                            _buildInfoChip(
                                                              Icons.local_hospital_outlined,
                                                              'فصيلة الدم: ${student?.type__blood_id}',
                                                              Colors.red,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Location Information (معلومات الموقع)
                                                      _buildSection(
                                                        'معلومات الموقع',
                                                        Icons.location_on_outlined,
                                                        [
                                                          if (student?.address != null)
                                                            _buildInfoChip(
                                                              Icons.home_outlined,
                                                              'العنوان: ${student?.address}',
                                                              Colors.orange,
                                                            ),
                                                          if (student?.city_name != null)
                                                            _buildInfoChip(
                                                              Icons.location_city_outlined,
                                                              'المدينة: ${student?.city_name}',
                                                              Colors.teal,
                                                            ),
                                                          if (student?.latitude != null && student?.longitude != null)
                                                            _buildInfoChip(
                                                              Icons.pin_drop_outlined,
                                                              'الإحداثيات: ${student?.latitude}, ${student?.longitude}',
                                                              Colors.indigo,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Academic Information (معلومات أكاديمية)
                                                      _buildSection(
                                                        'معلومات أكاديمية',
                                                        Icons.school_outlined,
                                                        [
                                                          if (student?.school_id != null)
                                                            _buildInfoChip(
                                                              Icons.business_outlined,
                                                              'رقم المدرسة: ${student?.school_id}',
                                                              Colors.brown,
                                                            ),
                                                          if (student?.grade_id != null)
                                                            _buildInfoChip(
                                                              Icons.grade_outlined,
                                                              'الصف: ${student?.grade_id}',
                                                              Colors.deepPurple,
                                                            ),
                                                          if (student?.classroom_id != null)
                                                            _buildInfoChip(
                                                              Icons.class_outlined,
                                                              'الفصل: ${student?.classroom_id}',
                                                              Colors.cyan,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Transportation Information (معلومات النقل)
                                                      _buildSection(
                                                        'معلومات النقل',
                                                        Icons.directions_bus_outlined,
                                                        [
                                                          if (student?.bus_id != null)
                                                            _buildInfoChip(
                                                              Icons.directions_bus_filled_outlined,
                                                              'رقم الحافلة: ${student?.bus_id}',
                                                              Colors.green,
                                                            ),
                                                          if (student?.trip_type != null)
                                                            _buildInfoChip(
                                                              Icons.swap_horiz_outlined,
                                                              'نوع الرحلة: ${student?.trip_type}',
                                                              Colors.amber,
                                                            ),
                                                          if (student?.attendant_driver_id != null)
                                                            _buildInfoChip(
                                                              Icons.person_outline,
                                                              'رقم السائق: ${student?.attendant_driver_id}',
                                                              Colors.blue,
                                                            ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  trailing: AnimatedContainer(
                                                    duration: const Duration(
                                                        milliseconds: 300),
                                                    width: 40,
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      color: selectedStudents
                                                              .any((s) =>
                                                                  s.id ==
                                                                  student!.id)
                                                          ? Colors.green
                                                              .withOpacity(0.1)
                                                          : Colors.blue
                                                              .withOpacity(0.1),
                                                    ),
                                                    child: IconButton(
                                                      icon: AnimatedSwitcher(
                                                        duration:
                                                            const Duration(
                                                                milliseconds:
                                                                    300),
                                                        transitionBuilder:
                                                            (Widget child,
                                                                Animation<
                                                                        double>
                                                                    animation) {
                                                          return RotationTransition(
                                                            turns: animation,
                                                            child:
                                                                ScaleTransition(
                                                              scale: animation,
                                                              child: child,
                                                            ),
                                                          );
                                                        },
                                                        child: Icon(
                                                          selectedStudents.any(
                                                                  (s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)
                                                              ? Icons
                                                                  .check_circle
                                                              : Icons
                                                                  .add_circle_outline,
                                                          key: ValueKey<bool>(
                                                              selectedStudents
                                                                  .any((s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)),
                                                          color: selectedStudents
                                                                  .any((s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)
                                                              ? Colors.green
                                                              : Colors.blue,
                                                          size: 24,
                                                        ),
                                                      ),
                                                      padding: EdgeInsets.zero,
                                                      tooltip: selectedStudents
                                                              .any((s) =>
                                                                  s.id ==
                                                                  student!.id)
                                                          ? "Already added"
                                                          : "Add student",
                                                      onPressed: () {
                                                        try {
                                                          if (!selectedStudents
                                                              .any((s) =>
                                                                  s.id ==
                                                                  student!
                                                                      .id)) {
                                                            setState(() {
                                                              selectedStudents
                                                                  .add(
                                                                      student!);
                                                            });
                                                            logger.i(
                                                                "Student added from search: ${student!.name} (ID: ${student.id})");
                                                            _ttsService.speak(
                                                                "Student ${student.name} added",
                                                                language:
                                                                    "en-US");
                                                          }
                                                        } catch (e) {
                                                          logger.e(
                                                              "Error adding student from search: $e");
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                )));
                                      },
                                    ));
                                  } else if (state
                                      is StudentFilterErrorStates) {
                                    logger.e("StudentFilterCubit: Error state");
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.error_outline, 
                                            color: Colors.red, 
                                            size: 48
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'No students found',
                                            style: TextStyle(color: Colors.red),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              )
                            : BlocBuilder<StudentBusCubit, StudentBusStates>(
                                builder: (context, state) {
                                  if (state is StudentBusLoadingStates &&
                                      !_isLoadingMore) {
                                    logger.i(
                                        "StudentBusCubit: Loading initial data");
                                    return const Center(
                                        child: CircularProgressIndicator());
                                  } else if (state is StudentBusSuccessStates) {
                                    // Create a new list instead of modifying the existing one
                                    if (state.current_page == 1) {
                                      studentsToAdd =
                                          List.from(state.studentModel ?? []);
                                    } else {
                                      // Create a new list with all items
                                      studentsToAdd = List.from(studentsToAdd)
                                        ..addAll(state.studentModel ?? []);
                                    }
                                    logger.i(
                                        "StudentBusCubit: Loaded ${studentsToAdd.length} students");

                                    if (studentsToAdd.isEmpty) {
                                      return Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(20),
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade50,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                Icons.school_outlined,
                                                size: 48,
                                                color: Colors.grey.shade400,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            CustomText(
                                              text: AppStrings.studentNotFound
                                                  .tr(),
                                              fontSize: 16,
                                              fontW: FontWeight.w500,
                                              color: Colors.grey.shade600,
                                            ),
                                            const SizedBox(height: 8),
                                            TextButton.icon(
                                              onPressed: () =>
                                                  _loadEmptyBusStudents(),
                                              icon: const Icon(Icons.refresh),
                                              label: const Text('Refresh'),
                                              style: TextButton.styleFrom(
                                                foregroundColor:
                                                    TColor.mainColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }

                                    return SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.6,
                                      child: ListView.builder(
                                        controller: _scrollController,
                                        itemCount: studentsToAdd.length +
                                            (_isLoadingMore ? 1 : 0),
                                        itemBuilder: (context, index) {
                                          if (index >= studentsToAdd.length) {
                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 16.0),
                                              child: Center(
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    const CircularProgressIndicator(
                                                        strokeWidth: 3),
                                                    const SizedBox(height: 12),
                                                    Text(
                                                      'Loading more students...',
                                                      style: TextStyle(
                                                          color: Colors
                                                              .grey.shade600),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }

                                          final student = studentsToAdd[index];
                                          if (student == null)
                                            return const SizedBox.shrink();

                                          return Card(
                                            elevation: 2,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 6),
                                            child: ExpansionTile(
                                              leading: Hero(
                                                tag: 'student_${student.id}',
                                                child: CircleAvatar(
                                                  radius: 25,
                                                  backgroundColor:
                                                      Colors.blue.shade100,
                                                  child: Text(
                                                    (student.name ?? '?')
                                                        .substring(0, 1)
                                                        .toUpperCase(),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 18),
                                                  ),
                                                ),
                                              ),
                                              title: Text(
                                                student.name ?? "",
                                                style: const TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black87,
                                                ),
                                              ),
                                              trailing: IconButton(
                                                icon: Icon(
                                                  selectedStudents.any((s) =>
                                                          s.id == student.id)
                                                      ? Icons.check_circle
                                                      : Icons.add_circle_outline,
                                                  color: selectedStudents.any(
                                                          (s) =>
                                                              s.id == student.id)
                                                      ? Colors.green
                                                      : Colors.grey,
                                                ),
                                                onPressed: () {
                                                  setState(() {
                                                    if (selectedStudents.any(
                                                        (s) => s.id == student.id)) {
                                                      selectedStudents.removeWhere(
                                                          (s) => s.id == student.id);
                                                    } else {
                                                      selectedStudents.add(student);
                                                    }
                                                  });
                                                },
                                              ),
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsets.all(16),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      // Personal Information Section (معلومات شخصية)
                                                      _buildSection(
                                                        'معلومات شخصية',
                                                        Icons.person_outline,
                                                        [
                                                          if (student.phone != null)
                                                            _buildInfoChip(
                                                              Icons.phone_outlined,
                                                              'رقم الهاتف: ${student.phone}',
                                                              Colors.blue,
                                                            ),
                                                          if (student.Date_Birth != null)
                                                            _buildInfoChip(
                                                              Icons.cake_outlined,
                                                              'تاريخ الميلاد: ${student.Date_Birth}',
                                                              Colors.purple,
                                                            ),
                                                          if (student.gender != null)
                                                            _buildInfoChip(
                                                              Icons.wc_outlined,
                                                              'الجنس: ${student.gender}',
                                                              Colors.pink,
                                                            ),
                                                          if (student.type__blood_id != null)
                                                            _buildInfoChip(
                                                              Icons.local_hospital_outlined,
                                                              'فصيلة الدم: ${student.type__blood_id}',
                                                              Colors.red,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Location Information (معلومات الموقع)
                                                      _buildSection(
                                                        'معلومات الموقع',
                                                        Icons.location_on_outlined,
                                                        [
                                                          if (student.address != null)
                                                            _buildInfoChip(
                                                              Icons.home_outlined,
                                                              'العنوان: ${student.address}',
                                                              Colors.orange,
                                                            ),
                                                          if (student.city_name != null)
                                                            _buildInfoChip(
                                                              Icons.location_city_outlined,
                                                              'المدينة: ${student.city_name}',
                                                              Colors.teal,
                                                            ),
                                                          if (student.latitude != null && student.longitude != null)
                                                            _buildInfoChip(
                                                              Icons.pin_drop_outlined,
                                                              'الإحداثيات: ${student.latitude}, ${student.longitude}',
                                                              Colors.indigo,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Academic Information (معلومات أكاديمية)
                                                      _buildSection(
                                                        'معلومات أكاديمية',
                                                        Icons.school_outlined,
                                                        [
                                                          if (student.schools != null)
                                                            _buildInfoChip(
                                                              Icons.business_outlined,
                                                              'رقم المدرسة: ${student.schools}',
                                                              Colors.brown,
                                                            ),
                                                          if (student.grade != null)
                                                            _buildInfoChip(
                                                              Icons.grade_outlined,
                                                              'الصف: ${student.grade!.name!}',
                                                              Colors.deepPurple,
                                                            ),
                                                          if (student.classroom != null)
                                                            _buildInfoChip(
                                                              Icons.class_outlined,
                                                              'الفصل: ${student.classroom}',
                                                              Colors.cyan,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Transportation Information (معلومات النقل)
                                                      _buildSection(
                                                        'معلومات النقل',
                                                        Icons.directions_bus_outlined,
                                                        [
                                                          if (student.bus_id != null)
                                                            _buildInfoChip(
                                                              Icons.directions_bus_filled_outlined,
                                                              'رقم الحافلة: ${student.bus_id}',
                                                              Colors.green,
                                                            ),
                                                          if (student.trip_type != null)
                                                            _buildInfoChip(
                                                              Icons.swap_horiz_outlined,
                                                              'نوع الرحلة: ${student.trip_type}',
                                                              Colors.amber,
                                                            ),
                                                          if (student.attendant_driver_id != null)
                                                            _buildInfoChip(
                                                              Icons.person_outline,
                                                              'رقم السائق: ${student.attendant_driver_id}',
                                                              Colors.blue,
                                                            ),
                                                            if ( student.bus  !=null &&student.bus!.id != null)
                                                                  _buildInfoChip(
                                                                    Icons.numbers_outlined,
                                                                    'Bus ID: ${student.bus!.id}',
                                                                    Colors.blue,
                                                                  ),
                                                        ],
                                                      ),
],
                                                  ),
                                                ),
                                              
                      ])
                                          );
                                        },
                                      ),
                                    );
                                  } else if (state is StudentBusErrorStates) {
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.error_outline, 
                                            color: Colors.red, 
                                            size: 48
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'No students found',
                                            style: TextStyle(color: Colors.red),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                },
                              ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
