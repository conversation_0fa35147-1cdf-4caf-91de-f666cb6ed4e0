import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_cubit.dart';
import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_states.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_drop_down_button.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:logger/logger.dart'; // Import the logger package.
import 'package:bus/bloc/student_bus_cubit/student_bus_cubit.dart';
import 'package:bus/bloc/student_bus_cubit/student_bus_states.dart';
import 'package:bus/bloc/student_cubit/student_filter_cubit.dart';
import 'package:bus/bloc/student_cubit/student_filter_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/student_widgets/custom_search_w.dart';
import 'package:bus/translations/local_keys.g.dart';

final Logger logger = Logger();

class TextToSpeechService {
  static const MethodChannel _channel = MethodChannel('com.example.tts');

  Future<void> speak(String text, {String language = "en-US"}) async {
    try {
      await _channel
          .invokeMethod('speak', {"text": text, "language": language});
      logger.i("TTS: Speaking text '$text' in language $language");
    } on PlatformException catch (e) {
      logger.e("TTS Error: ${e.message}");
    } catch (e) {
      logger.e("TTS Unknown Error: $e");
    }
  }

  Future<void> stop() async {
    try {
      await _channel.invokeMethod('stop');
      logger.i("TTS: Stop called successfully");
    } on PlatformException catch (e) {
      logger.e("TTS Stop Error: ${e.message}");
    } catch (e) {
      logger.e("TTS Stop Unknown Error: $e");
    }
  }
}


class FadeInWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  const FadeInWidget(
      {super.key,
      required this.child,
      this.duration = const Duration(milliseconds: 500)});

  @override
  State<FadeInWidget> createState() => _FadeInWidgetState();
}

class _FadeInWidgetState extends State<FadeInWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: widget.child,
    );
  }
}


class StudentToBusScreen extends StatefulWidget {
  static const String routeName = PathRouteName.student;
  final String busId;
  final String? busName;
  const StudentToBusScreen({super.key, this.busId = '', required this.busName});

  @override
  State<StudentToBusScreen> createState() => _StudentToBusScreenState();
}

class _StudentToBusScreenState extends State<StudentToBusScreen>
    with TickerProviderStateMixin {
  AnimationController? controller;
  AnimationController? controllerIcon;
  Animation<double>? opacity;
  TextEditingController? cSearchStudentName;

  List<StudentModel?> studentsToAdd = [];
  List<StudentModel> selectedStudents = [];
  bool searchCheckFilter = false;
  bool _isLoadingMore = false;
  late ScrollController _scrollController;
  int? selectedBusId;

  final TextToSpeechService _ttsService = TextToSpeechService();

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    if (children.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: TColor.mainColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 18, color: TColor.mainColor),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: children,
          ),
        ],
      ),
    );
  }

  // Helper method to build info chips
  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      margin: const EdgeInsets.only(right: 8, bottom: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color.withValues(alpha: 0.9),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build a compact student summary card
  Widget _buildStudentSummaryCard(StudentModel student, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: isSelected ? TColor.mainColor.withValues(alpha: 0.05) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? TColor.mainColor.withValues(alpha: 0.3) : Colors.grey.shade200,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? TColor.mainColor.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: isSelected ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            setState(() {
              if (isSelected) {
                selectedStudents.removeWhere((s) => s.id == student.id);
              } else {
                selectedStudents.add(student);
              }
            });

            // Provide haptic feedback
            HapticFeedback.lightImpact();

            // TTS feedback
            _ttsService.speak(
              isSelected ? "Student ${student.name} removed" : "Student ${student.name} added",
              language: "en-US"
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Student Avatar
                Hero(
                  tag: 'avatar_${student.id}',
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: isSelected
                            ? [TColor.mainColor, TColor.mainColor.withValues(alpha: 0.8)]
                            : [Colors.blue.shade400, Colors.blue.shade600],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: (isSelected ? TColor.mainColor : Colors.blue)
                              .withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        (student.name ?? '?').substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Student Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        student.name ?? "",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? TColor.mainColor : Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (student.grade?.name != null)
                        Text(
                          "Grade: ${student.grade!.name}",
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      if (student.bus?.name != null)
                        Text(
                          "Current Bus: ${student.bus!.name}",
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),

                // Selection Indicator
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? TColor.mainColor
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    isSelected ? Icons.check : Icons.add,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    cSearchStudentName = TextEditingController();
    cSearchStudentName!.addListener(() {
      if (cSearchStudentName!.text.isEmpty) {
        searchCheckFilter = false;
        // Load empty bus students when search is cleared
        _loadEmptyBusStudents();
      }
      setState(() {});
    });
    controller = AnimationController(
        duration: const Duration(milliseconds: 200), vsync: this);
    controllerIcon = AnimationController(
        duration: const Duration(milliseconds: 500), vsync: this);
    opacity = Tween<double>(begin: 0.0, end: 1.0).animate(controller!);

    // Initialize scroll controller and add listener for pagination
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // Load initial data with bus filter
    _loadEmptyBusStudents();

    // Load available buses
    context.read<BusesCubit>().getAllBuses();

    logger.i("StudentToBusScreen initialized");
  }

  // Load empty bus students with current page
  Future<void> _loadEmptyBusStudents({int page = 1}) {
    return context.read<StudentBusCubit>().getEmptyBusStudents(
          withoutBusId: int.parse(widget.busId),
          withBusId: selectedBusId,
          page: page,
        );
  }

  // Handle scroll for pagination
  void _onScroll() async {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      if (!searchCheckFilter) {
        final state = context.read<StudentBusCubit>().state;
        if (state is StudentBusSuccessStates && !_isLoadingMore) {
          if (state.current_page != null &&
              state.last_page != null &&
              state.current_page! < state.last_page!) {
            setState(() {
              _isLoadingMore = true;
            });
            await _loadEmptyBusStudents(page: state.current_page! + 1);
            setState(() {
              _isLoadingMore = false;
            });
          }
        }
      }
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    controllerIcon?.dispose();
    cSearchStudentName?.dispose();
    _scrollController.dispose();
    logger.i("StudentToBusScreen disposed");
    super.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) super.setState(fn);
  }

  void toggleAnimation() {
    if (controller?.status == AnimationStatus.dismissed) {
      controller?.forward();
    } else if (controller?.status == AnimationStatus.completed) {
      controller?.reverse();
    }
  }

  void toggleIcon() {
    if (controllerIcon?.status == AnimationStatus.dismissed) {
      controllerIcon?.forward();
    } else if (controllerIcon?.status == AnimationStatus.completed) {
      controllerIcon?.reverse();
    }
  }

  Widget _buildBusFilter() {
    return BlocBuilder<BusesCubit, BusesState>(
      builder: (context, state) {
        if (state is BusesLoadingStates) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    color: TColor.mainColor,
                    strokeWidth: 2,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Loading buses...',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          );
        } else if (state is BusesSuccessStates) {
          List<BusesInfoModel> buses = [
            BusesInfoModel(id: 0, name: AppStrings.selectBus.tr()),
            ...?state.busesDataModels?.data,
          ];
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: TColor.mainColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.directions_bus,
                    color: TColor.mainColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<int>(
                      isExpanded: true,
                      hint: Text(
                        AppStrings.selectBus.tr(),
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      value: selectedBusId ?? 0,
                      items: buses
                          .map((value) => DropdownMenuItem<int>(
                                value: value.id,
                                child: Text(
                                  value.name ?? '',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: value.id == 0
                                        ? FontWeight.w400
                                        : FontWeight.w500,
                                    color: value.id == 0
                                        ? Colors.grey.shade600
                                        : Colors.grey.shade800,
                                  ),
                                ),
                              ))
                          .toList(),
                      onChanged: (v) {
                        setState(() {
                          selectedBusId = v == 0 ? null : v;
                          _loadEmptyBusStudents(page: 1);
                        });

                        // Provide haptic feedback
                        HapticFeedback.selectionClick();
                      },
                      icon: Icon(
                        Icons.keyboard_arrow_down,
                        color: TColor.mainColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => BusesCubit()..getAllBuses()),
        ],
        child: Scaffold(
          bottomNavigationBar: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: BlocConsumer<AddRemoveStudentToBusCubit,
                  AddRemoveStudentToBusStates>(
                listener: (context, state) {
                  if (state is AddRemoveStudentToBusSuccessStates) {
                    logger.i("Students successfully added to bus");
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        backgroundColor: TColor.greenSuccess,
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        content: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.white, size: 24),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CustomText(
                                text: AppStrings.studentsAdded.tr(),
                                fontSize: 16,
                                maxLine: 2,
                                color: TColor.white,
                                fontW: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  } else if (state is AddRemoveStudentToBusErrorStates) {
                    logger.e("Error adding students to bus");
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        backgroundColor: TColor.redAccent,
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        content: const Row(
                          children: [
                            Icon(Icons.error, color: Colors.white, size: 24),
                            SizedBox(width: 12),
                            Expanded(
                              child: CustomText(
                                text: "Failed to add students",
                                fontSize: 16,
                                maxLine: 2,
                                color: TColor.white,
                                fontW: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                },
                builder: (context, state) {
                  final isLoading = state is AddRemoveStudentToBusLoadingStates;
                  final hasSelectedStudents = selectedStudents.isNotEmpty;

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    height: 56,
                    child: ElevatedButton(
                      onPressed: (!hasSelectedStudents || isLoading) ? null : () async {
                        try {
                          List<String> studentIds =
                              selectedStudents.map((e) => e.id!).toList();
                          logger.i(
                              "Attempting to add ${studentIds.length} students to bus ${widget.busId}");

                          // Provide haptic feedback
                          HapticFeedback.mediumImpact();

                          context
                              .read<AddRemoveStudentToBusCubit>()
                              .addStudents(
                                studentIds: studentIds,
                                busId: widget.busId,
                              );
                        } catch (e) {
                          logger.e("Error in Add button onTap: $e");
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: hasSelectedStudents
                            ? TColor.mainColor
                            : Colors.grey.shade300,
                        foregroundColor: Colors.white,
                        elevation: hasSelectedStudents ? 4 : 0,
                        shadowColor: TColor.mainColor.withValues(alpha: 0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.add, size: 24),
                                const SizedBox(width: 8),
                                Text(
                                  hasSelectedStudents
                                      ? 'Add ${selectedStudents.length} Student${selectedStudents.length == 1 ? '' : 's'}'
                                      : 'Select Students to Add',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  );
                },
              ),
            ),
          ),
          backgroundColor: const Color(0xFFF8F9FC),
          appBar: CustomAppBar(
            titleWidget: CustomText(
              text: '${AppStrings.addStudentToBus.tr()} (${widget.busName})',
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            leftWidget: InkWell(
              onTap: () {
                logger.i("Navigating back from StudentToBusScreen");
                Navigator.pop(context);
              },
              child: SvgPicture.asset(
                context.locale.toString() == "ar"
                    ? AppAssets.arrowBack
                    : AppAssets.forwardArrow,
                colorFilter:
                    const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                width: 25.w,
                height: 25.w,
              ),
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                // Header Section with Bus Filter
                Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      // Bus Filter Section
                      Container(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.filter_list,
                                  color: TColor.mainColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Filter by Bus',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey.shade800,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            _buildBusFilter(),
                          ],
                        ),
                      ),

                      // Search Section
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          border: Border(
                            top: BorderSide(color: Colors.grey.shade200),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.search,
                                  color: TColor.mainColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Search Students',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey.shade800,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.grey.shade200),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: CustomSearchW(
                                controller: cSearchStudentName,
                                onpressedSearch: () {
                                  try {
                                    FocusScope.of(context).unfocus();
                                    context
                                        .read<StudentFilterCubit>()
                                        .getStudentWithFilters(
                                          studentName:
                                              cSearchStudentName?.text ?? "",
                                        );
                                    searchCheckFilter = true;
                                    setState(() {});

                                    // Provide haptic feedback
                                    HapticFeedback.lightImpact();

                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(Icons.search, color: Colors.white, size: 20),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'Searching for "${cSearchStudentName?.text}"',
                                              style: const TextStyle(fontSize: 14),
                                            ),
                                          ),
                                        ],
                                      ),
                                      duration: const Duration(seconds: 2),
                                      backgroundColor: TColor.mainColor,
                                      behavior: SnackBarBehavior.floating,
                                      margin: const EdgeInsets.all(16),
                                      shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12)),
                                    ));
                                  } catch (e) {
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(Icons.error, color: Colors.white, size: 20),
                                          const SizedBox(width: 8),
                                          Expanded(child: Text('Search error: $e')),
                                        ],
                                      ),
                                      backgroundColor: Colors.red.shade400,
                                      behavior: SnackBarBehavior.floating,
                                      margin: const EdgeInsets.all(16),
                                      shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12)),
                                    ));
                                  }
                                },
                                hintText: AppStrings.searchStudentHint.tr(),
                                type: "student",
                                isSearch: true,
                                isIcon: false,
                              ),
                            ),
                            if (searchCheckFilter &&
                                cSearchStudentName?.text.isNotEmpty == true)
                              Container(
                                margin: const EdgeInsets.only(top: 12),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: TColor.mainColor.withValues(alpha: 0.05),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: TColor.mainColor.withValues(alpha: 0.2),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      size: 16,
                                      color: TColor.mainColor,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'Showing results for "${cSearchStudentName?.text}"',
                                        style: TextStyle(
                                          color: TColor.mainColor,
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    TextButton.icon(
                                      onPressed: () {
                                        cSearchStudentName?.clear();
                                        searchCheckFilter = false;
                                        setState(() {});
                                        _loadEmptyBusStudents();
                                        HapticFeedback.lightImpact();
                                      },
                                      style: TextButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(horizontal: 8),
                                        minimumSize: Size.zero,
                                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                      ),
                                      icon: Icon(
                                        Icons.clear,
                                        size: 16,
                                        color: TColor.mainColor,
                                      ),
                                      label: Text(
                                        'Clear',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: TColor.mainColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Selected Students Summary
                if (selectedStudents.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: TColor.mainColor.withValues(alpha: 0.05),
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: TColor.mainColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${selectedStudents.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            '${selectedStudents.length} student${selectedStudents.length == 1 ? '' : 's'} selected',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: TColor.mainColor,
                            ),
                          ),
                        ),
                        TextButton.icon(
                          onPressed: () {
                            setState(() {
                              selectedStudents.clear();
                            });
                            HapticFeedback.lightImpact();
                          },
                          icon: const Icon(Icons.clear_all, size: 18),
                          label: const Text('Clear All'),
                          style: TextButton.styleFrom(
                            foregroundColor: TColor.mainColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                // Students List
                Expanded(
                  child: searchCheckFilter
                            ? BlocBuilder<StudentFilterCubit,
                                StudentFilterState>(
                                builder: (context, state) {
                                  if (state is StudentFilterLoadingStates) {
                                    logger.i("StudentFilterCubit: Loading");
                                    return Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const CircularProgressIndicator(
                                              color: TColor.mainColor),
                                          const SizedBox(height: 20),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 24, vertical: 12),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(30),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.05),
                                                  blurRadius: 10,
                                                  offset: const Offset(0, 4),
                                                ),
                                              ],
                                            ),
                                            child: Text(
                                              'Loading students...',
                                              style: TextStyle(
                                                  color: Colors.grey.shade600),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else if (state
                                      is StudentFilterSuccessStates) {
                                    studentsToAdd = state.searchData
                                        .where((element) =>
                                            element!.bus_id.toString() !=
                                            widget.busId)
                                        .toList();
                                    if (studentsToAdd.isEmpty) {
                                      logger.w(
                                          "No students found in search results");
                                      return Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(20),
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade50,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                Icons.school_outlined,
                                                size: 48,
                                                color: Colors.grey.shade400,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            CustomText(
                                              text: AppStrings.studentNotFound
                                                  .tr(),
                                              fontSize: 16,
                                              fontW: FontWeight.w500,
                                              color: Colors.grey.shade600,
                                            ),
                                            const SizedBox(height: 8),
                                            TextButton.icon(
                                              onPressed: () =>
                                                  _loadEmptyBusStudents(),
                                              icon: const Icon(Icons.refresh),
                                              label: const Text('Refresh'),
                                              style: TextButton.styleFrom(
                                                foregroundColor:
                                                    TColor.mainColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }
                                    logger.i(
                                        "StudentFilterCubit: Loaded ${studentsToAdd.length} students");
                                    return Container(
                                      height: MediaQuery.of(context).size.height * 0.5, // Use 50% of screen height
                                      child: ListView.builder(
                                        controller: _scrollController,
                                        itemCount: studentsToAdd.length,
                                        itemBuilder: (context, index) {
                                        final student = studentsToAdd[index];
                                          return AnimatedContainer(
                                            duration: const Duration(
                                                milliseconds: 300),
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 16, vertical: 10),
                                            decoration: BoxDecoration(
                                              color: selectedStudents.any((s) =>
                                                      s.id == student!.id)
                                                  ? Colors.green.shade50.withOpacity(0.9)
                                                  : Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: selectedStudents.any(
                                                          (s) =>
                                                              s.id ==
                                                              student!.id)
                                                      ? Colors.green
                                                          .withOpacity(0.15)
                                                      : Colors.black
                                                          .withOpacity(0.08),
                                                  blurRadius: 15,
                                                  spreadRadius: 1,
                                                  offset: const Offset(0, 5),
                                                ),
                                              ],
                                            ),
                                            child: Material(
                                                color: Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                clipBehavior: Clip.antiAlias,
                                                child: ExpansionTile(
                                                  expandedCrossAxisAlignment: CrossAxisAlignment.start,
                                                  tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                                  leading: Hero(
                                                    tag:
                                                        'avatar_${student?.id ?? ''}',
                                                    child: Container(
                                                      width: 56,
                                                      height: 56,
                                                      decoration: BoxDecoration(
                                                        gradient:
                                                            LinearGradient(
                                                          colors: selectedStudents
                                                                  .any((s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)
                                                              ? [
                                                                  Colors.green
                                                                      .shade400,
                                                                  Colors.green
                                                                      .shade600,
                                                                ]
                                                              : [
                                                                  Colors.blue
                                                                      .shade400,
                                                                  Colors.blue
                                                                      .shade600,
                                                                ],
                                                          begin:
                                                              Alignment.topLeft,
                                                          end: Alignment
                                                              .bottomRight,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(24),
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: (selectedStudents.any((s) =>
                                                                        s.id ==
                                                                        student!
                                                                            .id)
                                                                    ? Colors
                                                                        .green
                                                                    : Colors
                                                                        .blue)
                                                                .withOpacity(
                                                                    0.2),
                                                            blurRadius: 10,
                                                            spreadRadius: 1,
                                                            offset:
                                                                const Offset(
                                                                    0, 4),
                                                          ),
                                                        ],
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          (student?.name ?? '?')
                                                              .substring(0, 1)
                                                              .toUpperCase(),
                                                          style: const TextStyle(
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            color: Colors.white,
                                                            fontSize: 26,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  title: Text(
                                                    student?.name ?? "",
                                                    style: TextStyle(
                                                        fontSize: 17,
                                                        fontWeight: FontWeight.w600,
                                                        color: selectedStudents.any((s) => s.id == student!.id)
                                                            ? Colors.green.shade700
                                                            : Colors.grey.shade900),
                                                  ),
                                                  // Use a Column to show multiple lines if class or bus name exist.
                                                  subtitle: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      // Personal Information Section (معلومات شخصية)
                                                      _buildSection(
                                                        'معلومات شخصية',
                                                        Icons.person_outline,
                                                        [
                                                          if (student?.phone != null)
                                                            _buildInfoChip(
                                                              Icons.phone_outlined,
                                                              'رقم الهاتف: ${student?.name}',
                                                              Colors.blue,
                                                            ),
                                                          if (student?.Date_Birth != null)
                                                            _buildInfoChip(
                                                              Icons.cake_outlined,
                                                              'تاريخ الميلاد: ${student?.Date_Birth}',
                                                              Colors.purple,
                                                            ),
                                                          if (student?.gender_id != null)
                                                            _buildInfoChip(
                                                              Icons.wc_outlined,
                                                              'الجنس: ${student?.gender_id}',
                                                              Colors.pink,
                                                            ),
                                                          if (student?.type__blood_id != null)
                                                            _buildInfoChip(
                                                              Icons.local_hospital_outlined,
                                                              'فصيلة الدم: ${student?.type__blood_id}',
                                                              Colors.red,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Location Information (معلومات الموقع)
                                                      _buildSection(
                                                        'معلومات الموقع',
                                                        Icons.location_on_outlined,
                                                        [
                                                          if (student?.address != null)
                                                            _buildInfoChip(
                                                              Icons.home_outlined,
                                                              'العنوان: ${student?.address}',
                                                              Colors.orange,
                                                            ),
                                                          if (student?.city_name != null)
                                                            _buildInfoChip(
                                                              Icons.location_city_outlined,
                                                              'المدينة: ${student?.city_name}',
                                                              Colors.teal,
                                                            ),
                                                          if (student?.latitude != null && student?.longitude != null)
                                                            _buildInfoChip(
                                                              Icons.pin_drop_outlined,
                                                              'الإحداثيات: ${student?.latitude}, ${student?.longitude}',
                                                              Colors.indigo,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Academic Information (معلومات أكاديمية)
                                                      _buildSection(
                                                        'معلومات أكاديمية',
                                                        Icons.school_outlined,
                                                        [
                                                          if (student?.school_id != null)
                                                            _buildInfoChip(
                                                              Icons.business_outlined,
                                                              'رقم المدرسة: ${student?.school_id}',
                                                              Colors.brown,
                                                            ),
                                                          if (student?.grade_id != null)
                                                            _buildInfoChip(
                                                              Icons.grade_outlined,
                                                              'الصف: ${student?.grade_id}',
                                                              Colors.deepPurple,
                                                            ),
                                                          if (student?.classroom_id != null)
                                                            _buildInfoChip(
                                                              Icons.class_outlined,
                                                              'الفصل: ${student?.classroom_id}',
                                                              Colors.cyan,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Transportation Information (معلومات النقل)
                                                      _buildSection(
                                                        'معلومات النقل',
                                                        Icons.directions_bus_outlined,
                                                        [
                                                          if (student?.bus_id != null)
                                                            _buildInfoChip(
                                                              Icons.directions_bus_filled_outlined,
                                                              'رقم الحافلة: ${student?.bus_id}',
                                                              Colors.green,
                                                            ),
                                                          if (student?.trip_type != null)
                                                            _buildInfoChip(
                                                              Icons.swap_horiz_outlined,
                                                              'نوع الرحلة: ${student?.trip_type}',
                                                              Colors.amber,
                                                            ),
                                                          if (student?.attendant_driver_id != null)
                                                            _buildInfoChip(
                                                              Icons.person_outline,
                                                              'رقم السائق: ${student?.attendant_driver_id}',
                                                              Colors.blue,
                                                            ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  trailing: AnimatedContainer(
                                                    duration: const Duration(
                                                        milliseconds: 300),
                                                    width: 40,
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      color: selectedStudents
                                                              .any((s) =>
                                                                  s.id ==
                                                                  student!.id)
                                                          ? Colors.green
                                                              .withOpacity(0.1)
                                                          : Colors.blue
                                                              .withOpacity(0.1),
                                                    ),
                                                    child: IconButton(
                                                      icon: AnimatedSwitcher(
                                                        duration:
                                                            const Duration(
                                                                milliseconds:
                                                                    300),
                                                        transitionBuilder:
                                                            (Widget child,
                                                                Animation<
                                                                        double>
                                                                    animation) {
                                                          return RotationTransition(
                                                            turns: animation,
                                                            child:
                                                                ScaleTransition(
                                                              scale: animation,
                                                              child: child,
                                                            ),
                                                          );
                                                        },
                                                        child: Icon(
                                                          selectedStudents.any(
                                                                  (s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)
                                                              ? Icons
                                                                  .check_circle
                                                              : Icons
                                                                  .add_circle_outline,
                                                          key: ValueKey<bool>(
                                                              selectedStudents
                                                                  .any((s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)),
                                                          color: selectedStudents
                                                                  .any((s) =>
                                                                      s.id ==
                                                                      student!
                                                                          .id)
                                                              ? Colors.green
                                                              : Colors.blue,
                                                          size: 24,
                                                        ),
                                                      ),
                                                      padding: EdgeInsets.zero,
                                                      tooltip: selectedStudents
                                                              .any((s) =>
                                                                  s.id ==
                                                                  student!.id)
                                                          ? "Already added"
                                                          : "Add student",
                                                      onPressed: () {
                                                        try {
                                                          if (!selectedStudents
                                                              .any((s) =>
                                                                  s.id ==
                                                                  student!
                                                                      .id)) {
                                                            setState(() {
                                                              selectedStudents
                                                                  .add(
                                                                      student!);
                                                            });
                                                            logger.i(
                                                                "Student added from search: ${student!.name} (ID: ${student.id})");
                                                            _ttsService.speak(
                                                                "Student ${student.name} added",
                                                                language:
                                                                    "en-US");
                                                          }
                                                        } catch (e) {
                                                          logger.e(
                                                              "Error adding student from search: $e");
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                )));
                                      },
                                    ));
                                  } else if (state
                                      is StudentFilterErrorStates) {
                                    logger.e("StudentFilterCubit: Error state");
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.error_outline,
                                            color: Colors.red,
                                            size: 48
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'No students found',
                                            style: TextStyle(color: Colors.red),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              )
                            : BlocBuilder<StudentBusCubit, StudentBusStates>(
                                builder: (context, state) {
                                  if (state is StudentBusLoadingStates &&
                                      !_isLoadingMore) {
                                    logger.i(
                                        "StudentBusCubit: Loading initial data");
                                    return const Center(
                                        child: CircularProgressIndicator());
                                  } else if (state is StudentBusSuccessStates) {
                                    // Create a new list instead of modifying the existing one
                                    if (state.current_page == 1) {
                                      studentsToAdd =
                                          List.from(state.studentModel ?? []);
                                    } else {
                                      // Create a new list with all items
                                      studentsToAdd = List.from(studentsToAdd)
                                        ..addAll(state.studentModel ?? []);
                                    }
                                    logger.i(
                                        "StudentBusCubit: Loaded ${studentsToAdd.length} students");

                                    if (studentsToAdd.isEmpty) {
                                      return Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(20),
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade50,
                                                shape: BoxShape.circle,
                                              ),
                                              child: Icon(
                                                Icons.school_outlined,
                                                size: 48,
                                                color: Colors.grey.shade400,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            CustomText(
                                              text: AppStrings.studentNotFound
                                                  .tr(),
                                              fontSize: 16,
                                              fontW: FontWeight.w500,
                                              color: Colors.grey.shade600,
                                            ),
                                            const SizedBox(height: 8),
                                            TextButton.icon(
                                              onPressed: () =>
                                                  _loadEmptyBusStudents(),
                                              icon: const Icon(Icons.refresh),
                                              label: const Text('Refresh'),
                                              style: TextButton.styleFrom(
                                                foregroundColor:
                                                    TColor.mainColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    }

                                    return SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.6,
                                      child: ListView.builder(
                                        controller: _scrollController,
                                        itemCount: studentsToAdd.length +
                                            (_isLoadingMore ? 1 : 0),
                                        itemBuilder: (context, index) {
                                          if (index >= studentsToAdd.length) {
                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 16.0),
                                              child: Center(
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    const CircularProgressIndicator(
                                                        strokeWidth: 3),
                                                    const SizedBox(height: 12),
                                                    Text(
                                                      'Loading more students...',
                                                      style: TextStyle(
                                                          color: Colors
                                                              .grey.shade600),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }

                                          final student = studentsToAdd[index];
                                          if (student == null)
                                            return const SizedBox.shrink();

                                          return Card(
                                            elevation: 2,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 6),
                                            child: ExpansionTile(
                                              leading: Hero(
                                                tag: 'student_${student.id}',
                                                child: CircleAvatar(
                                                  radius: 25,
                                                  backgroundColor:
                                                      Colors.blue.shade100,
                                                  child: Text(
                                                    (student.name ?? '?')
                                                        .substring(0, 1)
                                                        .toUpperCase(),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 18),
                                                  ),
                                                ),
                                              ),
                                              title: Text(
                                                student.name ?? "",
                                                style: const TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black87,
                                                ),
                                              ),
                                              trailing: IconButton(
                                                icon: Icon(
                                                  selectedStudents.any((s) =>
                                                          s.id == student.id)
                                                      ? Icons.check_circle
                                                      : Icons.add_circle_outline,
                                                  color: selectedStudents.any(
                                                          (s) =>
                                                              s.id == student.id)
                                                      ? Colors.green
                                                      : Colors.grey,
                                                ),
                                                onPressed: () {
                                                  setState(() {
                                                    if (selectedStudents.any(
                                                        (s) => s.id == student.id)) {
                                                      selectedStudents.removeWhere(
                                                          (s) => s.id == student.id);
                                                    } else {
                                                      selectedStudents.add(student);
                                                    }
                                                  });
                                                },
                                              ),
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsets.all(16),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      // Personal Information Section (معلومات شخصية)
                                                      _buildSection(
                                                        'معلومات شخصية',
                                                        Icons.person_outline,
                                                        [
                                                          if (student.phone != null)
                                                            _buildInfoChip(
                                                              Icons.phone_outlined,
                                                              'رقم الهاتف: ${student.phone}',
                                                              Colors.blue,
                                                            ),
                                                          if (student.Date_Birth != null)
                                                            _buildInfoChip(
                                                              Icons.cake_outlined,
                                                              'تاريخ الميلاد: ${student.Date_Birth}',
                                                              Colors.purple,
                                                            ),
                                                          if (student.gender != null)
                                                            _buildInfoChip(
                                                              Icons.wc_outlined,
                                                              'الجنس: ${student.gender}',
                                                              Colors.pink,
                                                            ),
                                                          if (student.type__blood_id != null)
                                                            _buildInfoChip(
                                                              Icons.local_hospital_outlined,
                                                              'فصيلة الدم: ${student.type__blood_id}',
                                                              Colors.red,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Location Information (معلومات الموقع)
                                                      _buildSection(
                                                        'معلومات الموقع',
                                                        Icons.location_on_outlined,
                                                        [
                                                          if (student.address != null)
                                                            _buildInfoChip(
                                                              Icons.home_outlined,
                                                              'العنوان: ${student.address}',
                                                              Colors.orange,
                                                            ),
                                                          if (student.city_name != null)
                                                            _buildInfoChip(
                                                              Icons.location_city_outlined,
                                                              'المدينة: ${student.city_name}',
                                                              Colors.teal,
                                                            ),
                                                          if (student.latitude != null && student.longitude != null)
                                                            _buildInfoChip(
                                                              Icons.pin_drop_outlined,
                                                              'الإحداثيات: ${student.latitude}, ${student.longitude}',
                                                              Colors.indigo,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Academic Information (معلومات أكاديمية)
                                                      _buildSection(
                                                        'معلومات أكاديمية',
                                                        Icons.school_outlined,
                                                        [
                                                          if (student.schools != null)
                                                            _buildInfoChip(
                                                              Icons.business_outlined,
                                                              'رقم المدرسة: ${student.schools}',
                                                              Colors.brown,
                                                            ),
                                                          if (student.grade != null)
                                                            _buildInfoChip(
                                                              Icons.grade_outlined,
                                                              'الصف: ${student.grade!.name!}',
                                                              Colors.deepPurple,
                                                            ),
                                                          if (student.classroom != null)
                                                            _buildInfoChip(
                                                              Icons.class_outlined,
                                                              'الفصل: ${student.classroom}',
                                                              Colors.cyan,
                                                            ),
                                                        ],
                                                      ),
                                                      const SizedBox(height: 12),

                                                      // Transportation Information (معلومات النقل)
                                                      _buildSection(
                                                        'معلومات النقل',
                                                        Icons.directions_bus_outlined,
                                                        [
                                                          if (student.bus_id != null)
                                                            _buildInfoChip(
                                                              Icons.directions_bus_filled_outlined,
                                                              'رقم الحافلة: ${student.bus_id}',
                                                              Colors.green,
                                                            ),
                                                          if (student.trip_type != null)
                                                            _buildInfoChip(
                                                              Icons.swap_horiz_outlined,
                                                              'نوع الرحلة: ${student.trip_type}',
                                                              Colors.amber,
                                                            ),
                                                          if (student.attendant_driver_id != null)
                                                            _buildInfoChip(
                                                              Icons.person_outline,
                                                              'رقم السائق: ${student.attendant_driver_id}',
                                                              Colors.blue,
                                                            ),
                                                            if ( student.bus  !=null &&student.bus!.id != null)
                                                                  _buildInfoChip(
                                                                    Icons.numbers_outlined,
                                                                    'Bus ID: ${student.bus!.id}',
                                                                    Colors.blue,
                                                                  ),
                                                        ],
                                                      ),
],
                                                  ),
                                                ),

                      ])
                                          );
                                        },
                                      ),
                                    );
                                  } else if (state is StudentBusErrorStates) {
                                    return const Center(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.error_outline,
                                            color: Colors.red,
                                            size: 48
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'No students found',
                                            style: TextStyle(color: Colors.red),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                },
                              ),
                ),
              ],
            ),
          ),
        ));
  }
}
