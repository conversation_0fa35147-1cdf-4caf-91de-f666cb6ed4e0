import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';

class StudentSearchWidget extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onSearchChanged;
  final VoidCallback onClearSearch;
  final bool isSearching;

  const StudentSearchWidget({
    Key? key,
    required this.controller,
    required this.onSearchChanged,
    required this.onClearSearch,
    this.isSearching = false,
  }) : super(key: key);

  @override
  State<StudentSearchWidget> createState() => _StudentSearchWidgetState();
}

class _StudentSearchWidgetState extends State<StudentSearchWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: _buildSearchField(),
          );
        },
      ),
    );
  }

  Widget _buildSearchField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isFocused
              ? TColor.mainColor.withValues(alpha: 0.5)
              : Colors.grey.shade200,
          width: _isFocused ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: _isFocused
                ? TColor.mainColor.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: _isFocused ? 12 : 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildSearchIcon(),
          Expanded(child: _buildTextField()),
          if (widget.controller.text.isNotEmpty) _buildClearButton(),
          if (widget.isSearching) _buildLoadingIndicator(),
        ],
      ),
    );
  }

  Widget _buildSearchIcon() {
    return Container(
      padding: const EdgeInsets.all(12),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: _isFocused
              ? TColor.mainColor.withValues(alpha: 0.15)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.search,
          color: _isFocused ? TColor.mainColor : Colors.grey.shade600,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildTextField() {
    return TextField(
      controller: widget.controller,
      onChanged: (value) {
        widget.onSearchChanged(value);
        setState(() {}); // Update UI for clear button visibility
      },
      onTap: () {
        setState(() => _isFocused = true);
        _animationController.forward();
        HapticFeedback.lightImpact();
      },
      onSubmitted: (_) {
        setState(() => _isFocused = false);
        _animationController.reverse();
      },
      onEditingComplete: () {
        setState(() => _isFocused = false);
        _animationController.reverse();
      },
      decoration: InputDecoration(
        hintText: AppStrings.searchStudentName.tr(),
        hintStyle: TextStyle(
          color: Colors.grey.shade500,
          fontSize: 14,
        ),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 16,
        ),
      ),
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildClearButton() {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            widget.onClearSearch();
            setState(() {});
            HapticFeedback.lightImpact();
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.clear,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          color: TColor.mainColor,
          strokeWidth: 2,
        ),
      ),
    );
  }
}

/// A widget that shows search results summary
class SearchResultsSummary extends StatelessWidget {
  final int totalResults;
  final String searchQuery;
  final bool isFiltered;
  final VoidCallback? onClearAll;

  const SearchResultsSummary({
    Key? key,
    required this.totalResults,
    required this.searchQuery,
    this.isFiltered = false,
    this.onClearAll,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (searchQuery.isEmpty && !isFiltered) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.shade200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            searchQuery.isNotEmpty ? Icons.search : Icons.filter_alt,
            size: 16,
            color: Colors.blue.shade700,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _buildSummaryText(),
              style: TextStyle(
                fontSize: 13,
                color: Colors.blue.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onClearAll != null)
            TextButton(
              onPressed: onClearAll,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                minimumSize: const Size(0, 32),
              ),
              child: Text(
                'Clear All',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _buildSummaryText() {
    if (searchQuery.isNotEmpty && isFiltered) {
      return 'Found $totalResults students for "$searchQuery" with filter applied';
    } else if (searchQuery.isNotEmpty) {
      return 'Found $totalResults students for "$searchQuery"';
    } else if (isFiltered) {
      return 'Showing $totalResults filtered students';
    }
    return 'Showing $totalResults students';
  }
}
