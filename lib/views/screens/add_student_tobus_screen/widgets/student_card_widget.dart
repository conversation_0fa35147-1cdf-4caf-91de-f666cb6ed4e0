import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/config/theme_colors.dart';
import '../components/info_chip_widget.dart';

class StudentCardWidget extends StatefulWidget {
  final StudentModel student;
  final bool isSelected;
  final Function(StudentModel) onToggleSelection;
  final VoidCallback? onTap;

  const StudentCardWidget({
    Key? key,
    required this.student,
    required this.isSelected,
    required this.onToggleSelection,
    this.onTap,
  }) : super(key: key);

  @override
  State<StudentCardWidget> createState() => _StudentCardWidgetState();
}

class _StudentCardWidgetState extends State<StudentCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: _buildCard(),
          ),
        );
      },
    );
  }

  Widget _buildCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        elevation: widget.isSelected ? 8 : 3,
        borderRadius: BorderRadius.circular(20),
        shadowColor: widget.isSelected
            ? TColor.mainColor.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.1),
        child: Container(
          decoration: BoxDecoration(
            gradient: widget.isSelected
                ? LinearGradient(
                    colors: [
                      Colors.green.shade50,
                      Colors.green.shade100.withValues(alpha: 0.7),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: widget.isSelected
                  ? Colors.green.withValues(alpha: 0.4)
                  : Colors.grey.withValues(alpha: 0.2),
              width: widget.isSelected ? 2 : 1,
            ),
          ),
          child: _buildExpansionTile(),
        ),
      ),
    );
  }

  Widget _buildExpansionTile() {
    return ExpansionTile(
      leading: _buildAvatar(),
      title: _buildTitle(),
      trailing: _buildActionButton(),
      children: [
        _buildExpandedContent(),
      ],
      onExpansionChanged: (expanded) {
        if (expanded) {
          _animationController.forward();
        } else {
          _animationController.reverse();
        }
      },
    );
  }

  Widget _buildAvatar() {
    return Hero(
      tag: 'student_${widget.student.id}',
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: widget.isSelected
                ? [Colors.green.shade400, Colors.green.shade600]
                : [Colors.blue.shade400, Colors.blue.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: widget.isSelected
                  ? Colors.green.withValues(alpha: 0.4)
                  : Colors.blue.withValues(alpha: 0.4),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Center(
          child: Text(
            (widget.student.name ?? '?').substring(0, 1).toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 24,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.student.name ?? "",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: widget.isSelected
                ? Colors.green.shade700
                : Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        _buildQuickInfo(),
      ],
    );
  }

  Widget _buildQuickInfo() {
    return Row(
      children: [
        if (widget.student.grade?.name != null) ...[
          InfoChipWidget(
            icon: Icons.school,
            text: widget.student.grade!.name!,
            color: Colors.blue,
            fontSize: 11,
          ),
          const SizedBox(width: 4),
        ],
        if (widget.student.bus?.name != null)
          InfoChipWidget(
            icon: Icons.directions_bus,
            text: widget.student.bus!.name!,
            color: Colors.orange,
            fontSize: 11,
          ),
      ],
    );
  }

  Widget _buildActionButton() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isSelected
              ? [Colors.green.shade400, Colors.green.shade600]
              : [Colors.grey.shade300, Colors.grey.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: widget.isSelected
                ? Colors.green.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.3),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: Icon(
            widget.isSelected ? Icons.check_circle : Icons.add_circle_outline,
            key: ValueKey(widget.isSelected),
            color: Colors.white,
            size: 24,
          ),
        ),
        onPressed: () {
          widget.onToggleSelection(widget.student);
          HapticFeedback.lightImpact();
        },
      ),
    );
  }

  Widget _buildExpandedContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailedInfo(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildDetailedInfo() {
    return Column(
      children: [
        if (widget.student.grade?.name != null)
          InfoSectionWidget(
            title: 'Academic Information',
            titleIcon: Icons.school,
            children: [
              InfoChipWidget(
                icon: Icons.class_,
                text: 'Grade: ${widget.student.grade!.name}',
                color: Colors.blue,
              ),

            ],
          ),
        const SizedBox(height: 12),
        if (widget.student.bus?.name != null)
          InfoSectionWidget(
            title: 'Transportation',
            titleIcon: Icons.directions_bus,
            children: [
              InfoChipWidget(
                icon: Icons.directions_bus,
                text: 'Current Bus: ${widget.student.bus!.name}',
                color: Colors.orange,
              ),
              if (widget.student.bus?.id != null)
                InfoChipWidget(
                  icon: Icons.tag,
                  text: 'Bus ID: ${widget.student.bus!.id}',
                  color: Colors.orange,
                ),
            ],
          ),
        const SizedBox(height: 12),
        InfoSectionWidget(
          title: 'Student Details',
          titleIcon: Icons.person,
          children: [
            InfoChipWidget(
              icon: Icons.badge,
              text: 'Student ID: ${widget.student.id}',
              color: Colors.purple,
            ),
            InfoChipWidget(
              icon: widget.isSelected ? Icons.check_circle : Icons.person_add,
              text: widget.isSelected ? 'Selected for Transfer' : 'Available for Transfer',
              color: widget.isSelected ? Colors.green : Colors.grey,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => widget.onToggleSelection(widget.student),
            icon: Icon(
              widget.isSelected ? Icons.remove : Icons.add,
              size: 18,
            ),
            label: Text(
              widget.isSelected ? 'Remove from Selection' : 'Add to Selection',
              style: const TextStyle(fontSize: 14),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.isSelected
                  ? Colors.red.shade400
                  : Colors.green.shade400,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        if (widget.onTap != null) ...[
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: widget.onTap,
            icon: const Icon(Icons.info_outline, size: 18),
            label: const Text('Details', style: TextStyle(fontSize: 14)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade400,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
