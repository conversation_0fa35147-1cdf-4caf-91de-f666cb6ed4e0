import 'package:bus/bloc/change_address_cubit/change_address_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/repo/change_adress_requests_repo.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:bus/widgets/custom_label_w.dart';
import 'package:bus/widgets/request_change_address_widgets/custom_accept_w.dart';
import 'package:bus/widgets/request_change_address_widgets/custom_c_request_address_w.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../bloc/notifications_cubit/notifications_cubit.dart';
import '../../../data/repo/notifications_repo.dart';
import '../../../utils/helper.dart';
import '../requests_address_change_screen/requests_address_change_screen.dart';

class RequestChangeAddressScreen extends StatelessWidget {
  static const String routeName = PathRouteName.requestChangeAddress;
  const RequestChangeAddressScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as CAdreessModels;
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.requestStatus.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          width: 1.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SBox(),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.studentName.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                    name: "${args.studentName}",
                  ),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.parents.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(name: "${args.parentName}"),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.old_address.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(name: "${args.old_address}"),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.newAddress.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(name: args.address ?? 'none'),
                  const SBox(h: 20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.bus.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(name: args.busName),
                  const SBox(
                    h: 30,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.0.w),
                    child: CustomLabelW(
                      label: AppStrings.requestStatus.tr(),
                    ),
                  ),
                  const SBox(h: 3),
                  CustomCRequestAddressW(
                      // textColor: (args.status) == 0
                      //     ? Colors.black
                      //     : (args.status) == 1
                      //         ? Colors.green
                      //         : Colors.red,
                      name: args.statusText!.text

                      // (args.status) == 0
                      //     ? AppStrings.newRequest.tr()
                      //     : (args.status) == 1
                      //         ? AppStrings.accepted.tr()
                      //         : AppStrings.refused.tr(),
                      ),
                  const SBox(
                    h: 30,
                  ),
                ],
              ),
              args.status == 3
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomAcceptW(
                          name: AppStrings.accept.tr(),
                          onTap: () async {
                            bool isValid = await CAdreessRepo()
                                .acceptChangeAddressRequest(
                                    changeAddressRequestId: args.id)
                                .whenComplete(() => CAdreessRepo().repo());
                            if (isValid) {
                              print(
                                  'a7aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa');
                              goBack();
                              navigateTo(
                                RequestsAddressChangeScreen(
                                  status: 'new',
                                  appBarTitle: AppStrings.newRequests.tr(),
                                ),
                                replace: true,
                              );

                              // ignore: use_build_context_synchronously
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.greenSuccess,
                                  content: CustomText(
                                    text: AppStrings.successfullyDone.tr(),
                                    fontSize: 18,
                                    maxLine: 5,
                                    color: TColor.white,
                                  ),
                                ),
                              );
                              NotificationsRepo().storeBusNotification(
                                busId: args.bus_id,
                                title: "تم قبول طلب تغيير عنوان",
                                body:
                                    "الطالب: ${args.studentName}\nالعنوان الجديد: ${args.address}",
                                route: "route",
                              );
                              NotificationsRepo().sendNotification(
                                deviceTokens: NotificationsCubit.get(context)
                                    .supervisorsFcmTokens!
                                    .data!,
                                title: "تم قبول طلب تغيير عنوان",
                                body:
                                    "الطالب: ${args.studentName}\nالعنوان الجديد: ${args.address}",
                              );
                            } else {
                              // Navigator.pop(context);
                              // ignore: use_build_context_synchronously
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text: "failed",
                                    fontSize: 18,
                                    maxLine: 5,
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                        const SBox(w: 8),
                        CustomAcceptW(
                          name: AppStrings.refusal.tr(),
                          onTap: () async {
                            print("refusal");
                            bool isValid = await CAdreessRepo()
                                .refuseChangeAddressRequest(
                                    changeAddressRequestId: args.id, text: '')
                                .whenComplete(() => CAdreessRepo().repo());
                            if (isValid) {
                              goBack();
                              navigateTo(
                                RequestsAddressChangeScreen(
                                  status: 'new',
                                  appBarTitle: AppStrings.newRequests.tr(),
                                ),
                                replace: true,
                              );
                              // ignore: use_build_context_synchronously
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.greenSuccess,
                                  content: CustomText(
                                    text: AppStrings.successfullyDone.tr(),
                                    fontSize: 18,
                                    maxLine: 5,
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            } else {
                              Navigator.pop(context);
                              // ignore: use_build_context_synchronously
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text: "failed",
                                    fontSize: 18,
                                    maxLine: 5,
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                      ],
                    )
                  : const SizedBox(),
              30.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }
}
