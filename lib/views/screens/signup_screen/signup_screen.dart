import 'package:bus/bloc/register_cubit/register_cubit.dart';
import 'package:bus/bloc/register_cubit/register_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/send_code_screen/send_code_screen.dart';
import 'package:bus/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';

import '../../../widgets/pick_location_widget.dart';

class SignupScreen extends StatefulWidget {
  static const String routeName = PathRouteName.signup;
  const SignupScreen({Key? key}) : super(key: key);

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  bool securityCheck = true;
  bool securityCheck1 = true;
  String? name;
  String? email;
  String? phone;
  String? address;
  TextEditingController password = TextEditingController();
  TextEditingController confirmedPassword = TextEditingController();

  LatLong? position;
  PickedData? pickedData;
  PickupLocationLocalModels? pickupLocationLocalModels;

  @override
  Widget build(BuildContext context) {
    // debugPrint(position);
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const SBox(h: 10),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              const SBox(h: 5),
              CustomText(
                text: AppStrings.signup.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const SBox(h: 20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                // height: 553.w,
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        const SBox(h: 40),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.person_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.schoolName.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          requierdNumber: 3,
                          saved: (value) {
                            name = value;
                          },
                          validation: AppStrings.name.tr(),
                          contentPaddingVertical: 0,
                          inputType: TextInputType.name,
                        ),
                        const SBox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.mail_outline_outlined,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.email.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          saved: (value) {
                            email = value;
                          },
                          requierdNumber: 6,
                          contentPaddingVertical: 0,
                          checkValidatorFunc: true,
                          validatorFunc: (value) {
                            if (value == null || value.isEmpty) {
                              return AppStrings.validEmail.tr();
                            } else if (value.length < 6) {
                              return "this field should be more than 6 characters long";
                            } else if (value.contains('+')) {
                              return "لا يمكن استخدام علامة + في البريد الإلكتروني";
                            }
                            return null;
                          },
                          inputType: TextInputType.emailAddress,
                        ),
                        const SBox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.location_on_outlined,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.address.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          validation: AppStrings.validAddress.tr(),
                          requierdNumber: 5,
                          contentPaddingVertical: 0,
                          saved: (value) {
                            address = value;
                            debugPrint(
                                "data ============================ $value");
                          },
                          inputType: TextInputType.name,
                        ),
                        const SBox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.phone_outlined,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          contentPaddingVertical: 0,
                          hintText: AppStrings.phoneNumber.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          requierdNumber: 10,
                          saved: (value) {
                            phone = value;
                          },
                          validation: AppStrings.validPhone.tr(),
                          inputType: TextInputType.phone,
                        ),
                        const SBox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          contentPaddingVertical: 0,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          controller: password,
                          hintText: AppStrings.password.tr(),
                          requierdNumber: 6,
                          saved: (value) {
                            password.text = value;
                          },
                          validation: AppStrings.validPassword.tr(),
                          security: securityCheck,
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck = !securityCheck;
                              });
                            },
                            child: securityCheck
                                ? const Icon(
                                    Icons.visibility_off,
                                    color: TColor.iconInputColor,
                                  )
                                : const Icon(
                                    Icons.visibility_outlined,
                                    color: TColor.iconInputColor,
                                  ),
                          ),
                        ),
                        const SBox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          contentPaddingVertical: 0,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          requierdNumber: 6,
                          controller: confirmedPassword,
                          hintText: AppStrings.confirmPassword.tr(),
                          saved: (value) {
                            confirmedPassword.text = value;
                          },
                          checkValidatorFunc: true,
                          validatorFunc: (value) {
                            if (password.text != value) {
                              return 'password not matching';
                            }
                            return null;
                          },
                          security: securityCheck1,
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck1 = !securityCheck1;
                              });
                            },
                            child: securityCheck1
                                ? const Icon(
                                    Icons.visibility_off,
                                    color: TColor.iconInputColor,
                                  )
                                : const Icon(
                                    Icons.visibility_outlined,
                                    color: TColor.iconInputColor,
                                  ),
                          ),
                        ),
                        const SBox(h: 10),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 37.w),
                          child: InkWell(
                            onTap: () async {
                              pickupLocationLocalModels = await Navigator.push(
                                  context, MaterialPageRoute(builder: (ctx) {
                                return const PickLocationWidget();
                              }));
                              setState(() {});
                              // debugPrint(pickedData.toString());
                              position = LatLong(
                                  pickupLocationLocalModels!.lat!,
                                  pickupLocationLocalModels!.long!);
                            },
                            child: Container(
                              width: 460.w,
                              height: 53.w,
                              decoration: BoxDecoration(
                                color: TColor.fillFormFieldB,
                                borderRadius: BorderRadius.circular(15.r),
                              ),
                              child: Padding(
                                padding: context.locale.toString() == "ar"
                                    ? EdgeInsets.only(top: 15.w, right: 15.w)
                                    : EdgeInsets.only(top: 15.w, left: 15.w),
                                child: CustomText(
                                  text: pickupLocationLocalModels != null
                                      ? pickupLocationLocalModels?.address
                                      : AppStrings.getLocations.tr(),
                                  color: TColor.tabColors,
                                  fontSize: 15,
                                  fontW: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SBox(h: 20),
                        BlocConsumer<RegisterCubit, RegisterStates>(
                          listener: (context, states) {
                            if (states.rState == ResponseState.success) {
                              // CacheHelper.putString(
                              //     "token", states.registerModels!.token!);
                              tempToken = states.registerModels!.token;
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.greenSuccess,
                                  content: CustomText(
                                    text: AppStrings.checkEmail.tr(),
                                    color: TColor.white,
                                    maxLine: 3,
                                  ),
                                ),
                              );

                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      SendCodeScreen(email: email),
                                ),
                              );
                            } else if (states.rState == ResponseState.failure) {
                              debugPrint(
                                  "Error:: states.registerModels?.massage = ${states.registerModels?.massage.toString()}");
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text: states.rMessage,
                                    color: TColor.white,
                                    maxLine: 3,
                                  ),
                                ),
                              );
                            }
                          },
                          builder: (context, states) {
                            if (states.rState != ResponseState.loading) {
                              return CustomButton(
                                text: AppStrings.signup.tr(),
                                onTap: () async {
                                  if (_formKey.currentState!.validate()) {
                                    _formKey.currentState!.save();
                                    context.read<RegisterCubit>().register(
                                          name: name,
                                          phone: phone,
                                          email: email,
                                          password: password.text,
                                          confirmedPassword:
                                              confirmedPassword.text,
                                          address: address,
                                          latitude:
                                              position?.latitude.toString(),
                                          longitude:
                                              position?.longitude.toString(),
                                          context: context,
                                        );
                                  }
                                },
                                width: 307,
                                height: 48,
                                radius: 15,
                                borderColor: TColor.mainColor,
                                bgColor: TColor.mainColor,
                              );
                            } else {
                              return const CircularProgressIndicator(
                                color: TColor.mainColor,
                              );
                            }
                            //  (states.rState == ResponseState.loading) {

                            //   } else {
                            //     return const Text("");
                            //   }
                          },
                        ),
                        const SBox(h: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomText(
                              text: AppStrings.haveAccount.tr(),
                              fontSize: 16,
                              fontW: FontWeight.w500,
                              color: TColor.textLogin,
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: CustomText(
                                text: AppStrings.login.tr(),
                                fontSize: 16,
                                fontW: FontWeight.w500,
                                color: TColor.mainColor,
                              ),
                            ),
                          ],
                        ),
                        const SBox(h: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
