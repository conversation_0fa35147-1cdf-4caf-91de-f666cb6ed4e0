import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/data/models/student_models/empty_bus_students_model.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../../config/global_variable.dart';

class StudRepoModel {
  List<StudentModel> studentModels = [];
  int lastPage = 0;
  int currentPage = 1;
  bool status = false;
}

class StudentRepo {
  final _dio = NetworkService();

  Future<StudRepoModel> repo({
    String? studentName = "",
    int? pageNumber = 1,
  }) async {
    List<StudentModel> studentModels = [];
    StudRepoModel responseRepo = StudRepoModel();

    try {
      final request = await _dio.get(
        url:
            "${ConfigBase.student}/index?page=$pageNumber&limit=10&text=$studentName",
        isAuth: true,
      );

      debugPrint(
          '-----------------------------------------------------------------------------------');
      debugPrint("student from repo ${request.statusCode}");

      if (request.statusCode == 200) {
        for (var element in (request.data["data"]["students"]["data"] as List<dynamic>)) {
          studentModels.add(StudentModel.fromMap(element));
        }

        responseRepo.status = true;
        responseRepo.currentPage =
            request.data["data"]["students"]["current_page"];
        responseRepo.lastPage =
            request.data["data"]["students"]["last_page"] ?? 0;
      } else {
        responseRepo.status = false;
      }

      responseRepo.studentModels = studentModels;
      return responseRepo;
    } on DioException catch (e, stackTrace) {
      debugPrint("Stack trace: $stackTrace");
      debugPrint("Dio error at student repo: ${e.message}");

      responseRepo.status = false;
      return responseRepo;
    } catch (e, stackTrace) {
      debugPrint("Stack trace: $stackTrace");
      debugPrint("catch error at student repo $e");
      responseRepo.status = false;
      return responseRepo;
    }
  }

  Future<StudRepoModel> busStudentsRepo({int? busId}) async {
    List<StudentModel> studentModels = [];
    StudRepoModel responseRepo = StudRepoModel();
    try {
      final request = await _dio.get(
        url: "general/buses/show/$busId",
        isAuth: true,
      );
      if (request.statusCode == 200) {
        for (var element in (request.data["data"]["students"] as List<dynamic>)) {
          studentModels.add(StudentModel.fromMap((element)));
        }
        responseRepo.status = true;
      } else {
        debugPrint("status = false");
        responseRepo.status = false;
      }
      responseRepo.studentModels = studentModels;
      return responseRepo;
    } catch (e, stackTrace) {
      debugPrint(stackTrace.toString());
      debugPrint("catch error at student repo $e");
      return responseRepo;
    }
  }

  Future<StudRepoModel> parentStudentsRepo({int? parentId}) async {
    // Initialize an empty list of student models
    final studentModels = <StudentModel>[];

    // Initialize the response object
    final responseRepo = StudRepoModel();

    try {
      // Construct the API URL
      final url = '${ConfigBase.parentShow}/$parentId';
      debugPrint("parent id $url");
      // Make the API request
      final response = await _dio.get(
        url: url,
        isAuth: true,
      );

      // Check if the response status code is 200 (OK)
      if (response.statusCode == 200) {
        // Parse the list of children from the response data
        final children = response.data["data"]["children"] as List<dynamic>;
        debugPrint("parent id $children");
        // Convert each child element into a StudentModel and add it to the list
        for (final element in children) {
          studentModels.add(StudentModel.fromMap(element));
        }

        // Set the status of the response to true
        responseRepo.status = true;
      } else {
        // Handle non-200 status codes
        debugPrint(
            "API request failed with status code: ${response.statusCode}");
        responseRepo.status = false;
      }

      // Assign the list of student models to the response object
      responseRepo.studentModels = studentModels;

      // Return the response object
      return responseRepo;
    } on DioException catch (e, stackTrace) {
      // Handle Dio-specific errors (e.g., network issues, timeouts)
      debugPrint("DioError in parentStudentsRepo: ${e.message}");
      debugPrint("Stack trace: $stackTrace");
      responseRepo.status = false;
      return responseRepo;
    } catch (e, stackTrace) {
      // Handle other unexpected errors (e.g., JSON parsing errors)
      debugPrint("Unexpected error in parentStudentsRepo: $e");
      debugPrint("Stack trace: $stackTrace");
      responseRepo.status = false;
      return responseRepo;
    }
  }

  Future<StudRepoModel> getEmptyBusStudents({
    int? withBusId,
    required int withoutBusId,
    int? classroomId,
    String? studentName,
    int page = 1,
  }) async {
    StudRepoModel responseRepo = StudRepoModel();

    try {
      String url = "buses/empty/students?page=$page";
      List<String> queryParams = [];

      if (withBusId != null) queryParams.add("with_bus_id=$withBusId");
      queryParams.add("without_bus_id=$withoutBusId");
      if (classroomId != null) queryParams.add("classroom_id=$classroomId");
      if (studentName != null && studentName.isNotEmpty) queryParams.add("name=$studentName");

      if (queryParams.isNotEmpty) {
        url += "&${queryParams.join('&')}";
      }

      final request = await _dio.get(
        url: url,
        isAuth: true,
      );

      if (request.statusCode == 200 && request.data != null) {
        final emptyBusResponse =
            EmptyBusStudentsResponse.fromMap(request.data);
        responseRepo.studentModels = emptyBusResponse.data;
        responseRepo.currentPage = emptyBusResponse.currentPage;
        responseRepo.lastPage = emptyBusResponse.lastPage;
        responseRepo.status = true;
      } else {
        responseRepo.status = false;
      }
      return responseRepo;
    } catch (e) {
      debugPrint("Error in getEmptyBusStudents: $e");
      responseRepo.status = false;
      return responseRepo;
    }
  }

  Future<bool> addOrRemoveStudentsToBus({
    required List<String> studentIds,
    String? busId,
  }) async {
    var dio = Dio();
    var formData = FormData();

    // Add student_ids as separate fields
    for (var id in studentIds) {
      formData.fields.add(MapEntry('student_id[]', id.toString()));
    }

    formData.fields.add(MapEntry('bus_id', '$busId'));
    formData.fields.add(const MapEntry('trip_type', "full_day"));

    printFormDataContents(formData);
    final authHeaders = {'Authorization': "Bearer $token"};
    try {
      final request = await dio.post(
        "${ConfigBase.baseUrl}buses/store/students",
        data: formData,
        options: Options(
          headers: authHeaders,
        ),
      );
      Logger().e("this is error ${request.data}");
      debugPrint('Response status: ${request.statusCode}');
      debugPrint('Response data: ${request.data}');

      /// Logger().e(_request.data);
      debugPrint(request.data.toString());
      return !request.data["errors"];
    } catch (e, stackTrace) {
      debugPrint(stackTrace.toString());
      debugPrint('$e');
      Logger().e("this is error ${e.toString()}");
      return false;
    }
  }

  void printFormDataContents(FormData formData) {
    debugPrint('**************');
    List<MapEntry<String, String>> formDataMap = formData.fields;
    debugPrint(formDataMap.toString());
  }
}
