import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/attendance_models/student_attendance_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StudentAttendanceItem extends StatelessWidget {
  final StudentAttendanceModel student;
  
  const StudentAttendanceItem({
    super.key,
    required this.student,
  });

  @override
  Widget build(BuildContext context) {
    final bool isPresent = student.is_present ?? false;
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        leading: CircleAvatar(
          radius: 25.r,
          backgroundColor: isPresent ? TColor.mainColor.withAlpha(30) : Colors.red.withAlpha(30),
          child: student.profile_image != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(25.r),
                  child: Image.network(
                    student.profile_image!,
                    width: 50.w,
                    height: 50.w,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.person,
                      size: 30.sp,
                      color: isPresent ? TColor.mainColor : Colors.red,
                    ),
                  ),
                )
              : Icon(
                  Icons.person,
                  size: 30.sp,
                  color: isPresent ? TColor.mainColor : Colors.red,
                ),
        ),
        title: CustomText(
          text: student.name ?? AppStrings.notFound.tr(),
          fontSize: 16,
          fontW: FontWeight.w600,
          color: TColor.text,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            CustomText(
              text: "${student.grade ?? ''} - ${student.classroom ?? ''}",
              fontSize: 14,
              fontW: FontWeight.w400,
              color: TColor.dialogName,
            ),
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  isPresent ? Icons.check_circle : Icons.cancel,
                  size: 16.sp,
                  color: isPresent ? Colors.green : Colors.red,
                ),
                SizedBox(width: 4.w),
                CustomText(
                  text: isPresent 
                      ? "${AppStrings.present.tr()} - ${student.attendance_time ?? ''}" 
                      : AppStrings.absent.tr(),
                  fontSize: 14,
                  fontW: FontWeight.w500,
                  color: isPresent ? Colors.green : Colors.red,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
